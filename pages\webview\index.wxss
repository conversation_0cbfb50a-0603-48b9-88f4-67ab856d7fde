.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.webview-container {
  flex: 1;
  width: 100%;
}

.error-container,
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.error-content,
.loading-content {
  text-align: center;
  max-width: 600rpx;
}

.error-icon,
.loading-icon {
  margin-bottom: 40rpx;
}

.error-message {
  margin-bottom: 60rpx;
}

.loading-text {
  margin-top: 20rpx;
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.error-actions .cu-btn {
  width: 100%;
}

/* 加载动画 */
.cuIcon-loading2 {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
