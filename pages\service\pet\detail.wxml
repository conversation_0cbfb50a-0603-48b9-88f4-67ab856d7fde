<view class="container pet-detail-container">
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:elif="{{!petInfo}}" class="error-container">
    <text class="error-text">宠物信息加载失败</text>
  </view>

  <view wx:else class="pet-detail-content">
    <!-- 宠物基本信息卡片 -->
    <view class="pet-info-card">
      <view class="pet-avatar-section" bind:tap="previewAvatar">
        <image
          src="{{petInfo.avatar || siteinfo.constant.pet[petInfo.type]}}"
          class="pet-avatar"
          mode="aspectFill"
        ></image>
      </view>

      <view class="pet-basic-info">
        <view class="pet-name-section">
          <text class="pet-name">{{petInfo.name}}</text>
          <view class="pet-gender" style="background-color: {{petInfo.gender === 1 ? 'rgba(122, 221, 252, 0.8)' : petInfo.gender === 2 ? 'rgba(255, 192, 218, 0.8)' : 'rgba(255, 235, 59, 0.8)'}};">
            {{petInfo.gender === 1 ? '弟弟' : petInfo.gender === 2 ? '妹妹' : '未知'}}
          </view>
        </view>

        <view class="pet-details">
          <view class="detail-item">
            <text class="detail-label">年龄：</text>
            <text class="detail-value">{{petInfo.formattedBri}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">品种：</text>
            <text class="detail-value">{{petInfo.breed || '未知'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">类型：</text>
            <text class="detail-value">{{petInfo.type === 'cat' ? '猫咪' : petInfo.type === 'dog' ? '狗狗' : '其他'}}</text>
          </view>
          <view class="detail-item" wx:if="{{petInfo.formattedBirthday}}">
            <text class="detail-label">生日：</text>
            <text class="detail-value">{{petInfo.formattedBirthday}}</text>
          </view>
        </view>
      </view>

      <view class="edit-btn" bind:tap="editPet">
        <text class="diy-icon-writefill edit-icon"></text>
        <text class="edit-text">编辑</text>
      </view>
    </view>

    <!-- 健康状态卡片 -->
    <view class="health-status-card">
      <view class="card-title">
        <text class="title-text">健康状态</text>
      </view>
      <view class="health-items">
        <view class="health-item">
          <text class="health-label">疫苗接种：</text>
          <text class="health-value {{petInfo.isVaccine ? 'status-yes' : 'status-no'}}">{{petInfo.isVaccine ? '已接种' : '未接种'}}</text>
        </view>
        <view class="health-item">
          <text class="health-label">绝育状态：</text>
          <text class="health-value {{petInfo.isSterilization ? 'status-yes' : 'status-no'}}">{{petInfo.isSterilization ? '已绝育' : '未绝育'}}</text>
        </view>
        <view class="health-item">
          <text class="health-label">驱虫状态：</text>
          <text class="health-value {{petInfo.isRepellent ? 'status-yes' : 'status-no'}}">{{petInfo.isRepellent ? '已驱虫' : '未驱虫'}}</text>
        </view>
      </view>
    </view>

    <!-- 条件显示：如果有足够的订单历史(>=3条)，显示订单列表；否则显示最后洗护时间 -->
    <view wx:if="{{hasOrderHistory}}" class="recent-orders-card">
      <view class="card-title">
        <text class="title-text">最近订单记录 ({{recentOrders.length}}条)</text>
      </view>
      <view class="orders-list">
        <view
          wx:for="{{recentOrders}}"
          wx:key="id"
          class="order-item"
          data-order-id="{{item.id}}"
          bind:tap="viewOrderDetail"
        >
          <view class="order-header">
            <text class="order-sn">{{item.sn}}</text>
            <text class="order-status">{{item.status}}</text>
          </view>
          <view class="order-details">
            <text class="order-time">下单时间：{{item.formattedCreatedAt}}</text>
            <text class="order-amount">金额：¥{{item.formattedTotalFee}}</text>
          </view>
          <view wx:if="{{item.formattedServiceTime}}" class="order-service-time">
            <text>服务时间：{{item.formattedServiceTime}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 如果没有足够的订单历史，显示最后洗护记录卡片 -->
    <view wx:else class="last-service-card">
      <view class="card-title">
        <text class="title-text">最后洗护记录</text>
        <text wx:if="{{recentOrders.length > 0 && recentOrders.length < 3}}" class="subtitle-text">订单记录较少，显示最后洗护时间</text>
      </view>
      <view wx:if="{{lastServiceData.lastServiceTime}}" class="service-info">
        <view class="service-item">
          <text class="service-label">洗护时间：</text>
          <text class="service-value">{{lastServiceData.formattedLastServiceTime}}</text>
        </view>
        <view class="service-item" wx:if="{{lastServiceData.orderSn}}">
          <text class="service-label">订单编号：</text>
          <text class="service-value">{{lastServiceData.orderSn}}</text>
        </view>
        <view class="service-item" wx:if="{{lastServiceData.formattedServiceTime}}">
          <text class="service-label">预约时间：</text>
          <text class="service-value">{{lastServiceData.formattedServiceTime}}</text>
        </view>
      </view>
      <view wx:else class="no-service">
        <text class="no-service-text">{{lastServiceData.message || '暂无洗护记录'}}</text>
      </view>
    </view>
  </view>
</view>
