import addressApi from "../../api/modules/address";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 弹窗标题
    title: {
      type: String,
      value: '修改服务地址'
    },
    // 初始地址数据
    initialAddress: {
      type: Object,
      value: {}
    },
    // 用户信息
    userInfo: {
      type: Object,
      value: null
    },
    // 是否显示坐标信息
    showCoordinates: {
      type: Boolean,
      value: false
    },
    // 按钮文本配置
    cancelText: {
      type: String,
      value: '取消'
    },
    confirmText: {
      type: String,
      value: '确认修改'
    },
    loadingText: {
      type: String,
      value: '修改中...'
    },
    // 输入框占位符配置
    addressPlaceholder: {
      type: String,
      value: '请输入服务地址'
    },
    detailPlaceholder: {
      type: String,
      value: '请输入详细地址'
    },
    remarkPlaceholder: {
      type: String,
      value: '如：小区东门'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 地址表单数据
    addressForm: {
      address: '',
      addressDetail: '',
      addressRemark: '',
      longitude: null,
      latitude: null,
      addressId: null
    },
    // 选择的地址类型：saved(已保存) | manual(地图选择)
    selectedAddressType: 'manual',
    // 用户地址列表
    userAddressList: [],
    // 是否正在加载
    loading: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.triggerEvent('close');
    },

    /**
     * 选择地址类型
     */
    selectAddressType(e) {
      const { type } = e.currentTarget.dataset;
      this.setData({ selectedAddressType: type });
      
      // 如果选择已保存地址，加载地址列表
      if (type === 'saved') {
        this.loadUserAddressList();
      }
    },

    /**
     * 选择已保存的地址
     */
    selectSavedAddress(e) {
      const { address } = e.currentTarget.dataset;
      
      // 如果点击的是已选中的地址，则取消选择
      if (this.data.addressForm.addressId === address.id) {
        this.setData({
          'addressForm.addressId': null,
          'addressForm.address': '',
          'addressForm.addressDetail': '',
          'addressForm.longitude': null,
          'addressForm.latitude': null
        });
        return;
      }

      // 选择新地址
      this.setData({
        'addressForm.addressId': address.id,
        'addressForm.address': address.addressText || '',
        'addressForm.addressDetail': address.detailAddress || '',
        'addressForm.longitude': address.longitude || null,
        'addressForm.latitude': address.latitude || null
      });
    },

    /**
     * 地址输入
     */
    onAddressInput(e) {
      this.setData({
        'addressForm.address': e.detail.value,
        'addressForm.addressId': null, // 手动输入时清除地址ID
        'addressForm.longitude': null, // 手动输入时清除经纬度
        'addressForm.latitude': null
      });
    },

    /**
     * 详细地址输入
     */
    onAddressDetailInput(e) {
      this.setData({
        'addressForm.addressDetail': e.detail.value
      });
    },

    /**
     * 地址备注输入
     */
    onAddressRemarkInput(e) {
      this.setData({
        'addressForm.addressRemark': e.detail.value
      });
    },

    /**
     * 选择地图位置
     */
    async chooseMapLocation() {
      try {
        const location = await wx.chooseLocation();
        this.setData({
          'addressForm.address': location.address,
          'addressForm.addressDetail': location.name,
          'addressForm.longitude': location.longitude,
          'addressForm.latitude': location.latitude,
          'addressForm.addressId': null // 地图选择时清除地址ID
        });
      } catch (error) {
        console.error('选择地图位置失败:', error);
        if (error.errMsg && error.errMsg.includes('cancel')) {
          // 用户取消选择，不显示错误提示
          return;
        }
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        });
      }
    },

    /**
     * 确认修改地址
     */
    async confirmModify() {
      const { addressForm, loading } = this.data;

      // 如果正在加载中，防止重复提交
      if (loading) {
        return;
      }

      // 验证必填字段
      if (!addressForm.address.trim()) {
        wx.showToast({
          title: '请输入服务地址',
          icon: 'none'
        });
        return;
      }

      if (!addressForm.addressDetail.trim()) {
        wx.showToast({
          title: '请输入详细地址',
          icon: 'none'
        });
        return;
      }

      // 触发确认事件，将数据传递给父组件
      this.triggerEvent('confirm', {
        addressForm: { ...addressForm }
      });
    },

    /**
     * 加载用户地址列表
     */
    async loadUserAddressList() {
      try {
        const { userInfo } = this.data;

        if (!userInfo) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        if (!userInfo.id) {
          wx.showToast({
            title: '用户信息异常',
            icon: 'none'
          });
          return;
        }

        const addressList = await addressApi.list(userInfo.id);
        this.setData({ userAddressList: addressList || [] });
      } catch (error) {
        console.error('加载地址列表失败:', error);
        wx.showToast({
          title: '加载地址列表失败',
          icon: 'none'
        });
      }
    },

    /**
     * 初始化地址表单数据
     */
    initAddressForm(initialAddress = {}) {
      this.setData({
        addressForm: {
          address: initialAddress.address || '',
          addressDetail: initialAddress.addressDetail || '',
          addressRemark: initialAddress.addressRemark || '',
          longitude: initialAddress.longitude || null,
          latitude: initialAddress.latitude || null,
          addressId: initialAddress.addressId || null
        }
      });
    },

    /**
     * 设置加载状态
     */
    setLoading(loading) {
      this.setData({ loading });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'show': function(show) {
      if (show) {
        // 弹窗显示时初始化数据
        this.initAddressForm(this.data.initialAddress);
        // 如果有用户信息且选择已保存地址类型，加载地址列表
        if (this.data.userInfo && this.data.selectedAddressType === 'saved') {
          this.loadUserAddressList();
        }
      }
    },
    'initialAddress': function(initialAddress) {
      if (this.data.show) {
        this.initAddressForm(initialAddress);
      }
    },
    'userInfo': function(userInfo) {
      // 用户信息变化时，如果弹窗显示且选择已保存地址，重新加载地址列表
      if (this.data.show && this.data.selectedAddressType === 'saved' && userInfo) {
        this.loadUserAddressList();
      }
    }
  }
});
