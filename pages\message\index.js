// pages/message/index.js
const utils = require('../utils/util');
const Session = require('../../common/Session.js').default;
import messageApi from '../../api/modules/message.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    newsList: [],
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getUserInfo();
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  // 获取用户信息
  getUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo) {
      return wx.redirectTo({
        url: "/pages/login/index",
      });
    }
    this.setData({ userInfo });
    this.loadMessageList();
  },

  // 加载消息列表
  async loadMessageList() {
    if (this.data.loading) return;

    this.setData({ loading: true });
    try {
      const res = await messageApi.getMessageList(this.data.userInfo.id);
      console.log('消息列表API响应:', res);

      // 处理不同的数据格式
      let messageList = [];
      if (Array.isArray(res)) {
        // 如果直接返回数组
        messageList = res;
      } else if (res && res.list && Array.isArray(res.list)) {
        // 如果返回对象包含list数组
        messageList = res.list;
      } else if (res && res.data && Array.isArray(res.data)) {
        // 如果返回对象包含data数组
        messageList = res.data;
      } else if (res && typeof res === 'object') {
        // 如果返回单个消息对象，转换为数组
        messageList = [res];
      }

      if (messageList.length >= 0) {
        const formattedList = messageList.map(item => ({
          ...item,
          time: utils.formatDate(new Date(item.createdAt || item.readAt || Date.now())),
          icon: this.getMessageIcon(item.type),
          content: item.content || '暂无内容'
        }));
        console.log('格式化后的消息列表:', formattedList);
        this.setData({ newsList: formattedList });
      } else {
        this.setData({ newsList: [] });
      }
    } catch (error) {
      console.error('获取消息列表失败:', error);
      // 在开发阶段，如果API不可用，显示测试数据
      if (error.message && error.message.includes('Network Error')) {
        this.loadTestData();
      } else {
        wx.showToast({
          title: '获取消息失败',
          icon: 'none'
        });
      }
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载测试数据（开发阶段使用）
  loadTestData() {
    const testData = [
      {
        id: '1',
        type: 'system',
        title: '系统通知',
        content: '您的洗护订单已接单，师傅正在赶往您的地址。',
        createdAt: new Date().toISOString(),
        isRead: false,
        count: 1,
        extraData: JSON.stringify({
          orderId: 'ORD123456',
          orderNo: 'SN202501060001',
          serviceTime: new Date().toISOString(),
          serviceAddress: '北京市朝阳区某某小区'
        })
      },
      {
        id: '2',
        type: 'platform',
        title: '平台消息',
        content: '恭喜您获得新用户专享优惠券，快去使用吧！',
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        isRead: true,
        count: 0,
        extraData: null
      },
      {
        id: '3',
        type: 'order',
        title: '订单消息',
        content: '您的洗护服务已完成，请查看服务照片并进行评价。',
        createdAt: new Date(Date.now() - 7200000).toISOString(),
        isRead: false,
        count: 2,
        extraData: JSON.stringify({
          orderId: 'ORD123455',
          orderNo: 'SN202501050001',
          amount: '88.00',
          petInfo: '小白（金毛，2岁）'
        })
      }
    ];

    const formattedList = testData.map(item => ({
      ...item,
      time: utils.formatDate(new Date(item.createdAt)),
      icon: this.getMessageIcon(item.type)
    }));

    this.setData({ newsList: formattedList });

    console.log('已加载测试数据，用于开发调试');
  },

  // 根据消息类型获取图标
  getMessageIcon(type) {
    const iconMap = {
      'system': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
      'platform': '//xian7.zos.ctyun.cn/pet/static/msg2.png',
      'order': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
    };
    return iconMap[type] || '//xian7.zos.ctyun.cn/pet/static/msg1.png';
  },

  // 点击消息项
  onMessageTap(e) {
    const { index } = e.currentTarget.dataset;
    const message = this.data.newsList[index];
    if (message) {
      // 标记为已读
      this.markAsRead(message.id, index);
      // 跳转到详情页
      wx.navigateTo({
        url: `/pages/message/messageDetail/index?id=${message.id}`
      });
    }
  },

  // 标记消息为已读
  async markAsRead(messageId, index) {
    try {
      await messageApi.markAsRead(messageId);
      // 更新本地数据
      const newsList = [...this.data.newsList];
      if (newsList[index]) {
        newsList[index].isRead = true;
        newsList[index].count = 0;
        this.setData({ newsList });
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('消息页面显示，当前数据:', this.data);
    // 页面显示时刷新消息列表
    if (this.data.userInfo) {
      this.loadMessageList();
    } else {
      console.log('用户信息不存在，重新获取');
      this.getUserInfo();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadMessageList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
});
