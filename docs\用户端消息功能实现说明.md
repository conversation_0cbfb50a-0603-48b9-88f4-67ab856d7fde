# 用户端消息功能实现说明

## 功能概述

基于员工端的消息功能实现，为用户端实现了完整的消息模块，包括：

- 消息列表展示（已读/未读状态）
- 消息详情查看
- 实时消息推送（WebSocket）
- 消息已读标记
- 下拉刷新
- 空状态处理
- 消息分类（系统消息、平台消息、订单消息）

## 实现的文件

### 1. API配置更新
- **文件**: `api/config.js`
- **更新内容**:
  - 添加了WebSocket配置 `socketUrl`
  - 添加了消息相关的API接口配置 `apiUrls.message`

### 2. 消息API模块
- **文件**: `api/modules/message.js`
- **功能**: 封装所有消息相关的API请求
- **接口**:
  - `getMessageList(userId, type)` - 获取消息列表
  - `getMessageDetail(messageId)` - 获取消息详情
  - `markAsRead(messageId)` - 标记消息为已读
  - `markAllAsRead(userId)` - 标记所有消息为已读
  - `deleteMessage(messageId)` - 删除消息
  - `getUnreadCount(userId)` - 获取未读消息数量

### 3. 消息列表页面
- **文件**: `pages/message/index.js`
- **更新内容**:
  - 添加用户身份验证
  - 实现消息列表加载和格式化
  - 添加消息图标映射
  - 实现点击消息跳转详情并标记已读
  - 添加下拉刷新功能
  - 错误处理和加载状态管理

- **文件**: `pages/message/index.wxml`
- **更新内容**:
  - 添加点击事件绑定
  - 动态显示消息图标
  - 添加已读/未读状态样式
  - 添加未读指示器

- **文件**: `pages/message/index.wxss`
- **更新内容**:
  - 添加消息状态样式（已读/未读）
  - 添加未读指示器样式

- **文件**: `pages/message/index.json`
- **更新内容**:
  - 启用下拉刷新功能

### 4. 消息详情页面
- **新建文件**: `pages/message/messageDetail/index.js`
- **功能**:
  - 消息详情展示
  - 自动标记已读
  - 动态设置页面标题
  - 解析和展示额外数据（订单信息等）

- **新建文件**: `pages/message/messageDetail/index.wxml`
- **功能**: 消息详情页面模板

- **新建文件**: `pages/message/messageDetail/index.wxss`
- **功能**: 消息详情页面样式

- **新建文件**: `pages/message/messageDetail/index.json`
- **功能**: 消息详情页面配置

### 5. WebSocket连接管理
- **文件**: `pages/index/index.js`
- **更新内容**:
  - 添加WebSocket连接和管理逻辑
  - 实现自动重连机制
  - 处理不同类型的通知消息
  - 在页面显示时连接WebSocket
  - 在页面卸载时清理连接

### 6. 页面路由配置
- **文件**: `app.json`
- **更新内容**: 添加消息详情页面路由

## 主要功能特性

### 1. 消息状态管理
- **未读消息**: 蓝色左边框，未读指示器，正常透明度
- **已读消息**: 降低透明度，无未读指示器

### 2. 消息类型图标
- **系统消息**: `msg1.png`
- **平台消息**: `msg2.png`
- **订单消息**: `msg1.png`

### 3. WebSocket实时通知
- 自动连接和重连
- 支持多种通知类型（订单、消息等）
- 最大重连次数限制（5次）
- 指数退避重连策略

### 4. 用户体验优化
- 下拉刷新
- 加载状态显示
- 错误处理和提示
- 空状态展示
- 页面标题动态设置

## 使用说明

### 1. 开发环境配置
确保在 `api/config.js` 中正确配置：
```javascript
baseUrl: "http://your-api-domain:port",
socketUrl: "ws://your-websocket-domain:port",
```

### 2. 后端API要求
需要后端提供以下API接口：
- `GET /api/message/list/{userId}` - 获取消息列表
- `GET /api/message/detail/{messageId}` - 获取消息详情
- `POST /api/message/mark-read/{messageId}` - 标记已读
- `POST /api/message/mark-all-read/{userId}` - 全部标记已读
- `DELETE /api/message/delete/{messageId}` - 删除消息
- `GET /api/message/unread-count/{userId}` - 未读数量

### 3. WebSocket服务要求
WebSocket服务需要支持以下消息格式：
```javascript
{
  type: "消息类型(new_order/cancel_order/message)",
  message: "通知消息内容",
  data: {
    // 其他业务相关数据
  }
}
```

### 4. 测试方法
1. 在微信开发者工具中打开项目
2. 确保后端API服务正常运行
3. 确保WebSocket服务正常运行
4. 登录用户账号
5. 进入消息页面测试各项功能

## 扩展功能

### 1. 消息删除功能
可以在消息列表页面添加删除功能：
```javascript
async deleteMessage(messageId, index) {
  try {
    await messageApi.deleteMessage(messageId);
    const newsList = [...this.data.newsList];
    newsList.splice(index, 1);
    this.setData({ newsList });
    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('删除消息失败:', error);
  }
}
```

### 2. 全部标记已读功能
```javascript
async markAllAsRead() {
  try {
    await messageApi.markAllAsRead(this.data.userInfo.id);
    const newsList = this.data.newsList.map(item => ({
      ...item,
      isRead: true,
      count: 0
    }));
    this.setData({ newsList });
  } catch (error) {
    console.error('标记全部已读失败:', error);
  }
}
```

### 3. 消息分页加载
可以添加分页功能以支持大量消息的加载。

## 注意事项

1. **用户身份验证**: 确保用户已登录才能访问消息功能
2. **错误处理**: 网络异常时的友好提示
3. **性能优化**: 大量消息时考虑虚拟列表或分页加载
4. **WebSocket管理**: 合理管理连接生命周期，避免内存泄漏
5. **权限控制**: 确保用户只能访问自己的消息

## 总结

本次实现完整地移植了员工端的消息功能到用户端，包括：
- 完整的消息列表和详情功能
- 实时WebSocket通知
- 良好的用户体验设计
- 完善的错误处理机制

所有功能都遵循了项目的现有代码规范和设计模式，可以无缝集成到现有系统中。
