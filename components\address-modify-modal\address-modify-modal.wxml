<!-- 地址修改弹窗组件 -->
<view wx:if="{{show}}" class="address-modify-modal">
  <view class="modal-mask" bindtap="closeModal"></view>
  <view class="modal-content">
    <!-- 弹窗头部 -->
    <view class="modal-header">
      <text class="modal-title">{{title || '修改服务地址'}}</text>
      <text class="modal-close" bindtap="closeModal">×</text>
    </view>

    <!-- 选择位置方式 -->
    <view class="address-type-selector">
      <view class="section-label">选择位置</view>
      <view class="type-buttons">
        <view
          class="type-btn {{selectedAddressType === 'saved' ? 'active' : ''}}"
          data-type="saved"
          bindtap="selectAddressType"
        >
          获取已保存位置
        </view>
        <view
          class="type-btn {{selectedAddressType === 'manual' ? 'active' : ''}}"
          data-type="manual"
          bindtap="selectAddressType"
        >
          地图选择
        </view>
      </view>
    </view>

    <!-- 已保存地址列表 -->
    <view wx:if="{{selectedAddressType === 'saved'}}" class="saved-address-section">
      <scroll-view class="saved-address-list" scroll-y>
        <view
          wx:for="{{userAddressList}}"
          wx:key="id"
          class="address-card {{addressForm.addressId === item.id ? 'selected' : ''}}"
          data-address="{{item}}"
          bindtap="selectSavedAddress"
        >
          <!-- 地址主要信息 -->
          <view class="address-main-info">
            <text class="address-title">{{item.addressText}}</text>
            <text class="address-detail-text">{{item.detailAddress}}</text>
          </view>

          <!-- 地址标签和状态 -->
          <view class="address-tags">
            <view wx:if="{{item.isDefault}}" class="default-badge">默认</view>
            <view wx:if="{{addressForm.addressId === item.id}}" class="selected-badge">
              <text class="selected-icon">✓</text>
            </view>
          </view>
        </view>

        <view wx:if="{{userAddressList.length === 0}}" class="empty-address-state">
          <view class="empty-icon">📍</view>
          <text class="empty-text">暂无保存的地址</text>
          <text class="empty-tip">请先在地址管理中添加地址</text>
        </view>
      </scroll-view>
    </view>

    <!-- 地图选择按钮 -->
    <view wx:if="{{selectedAddressType === 'manual'}}" class="map-select-section">
      <view class="map-select-card" bindtap="chooseMapLocation">
        <view class="map-select-content">
          <view class="map-icon-wrapper">
            <text class="map-icon">📍</text>
          </view>
          <view class="map-text-wrapper">
            <text class="map-title">选择地图位置</text>
            <text class="map-subtitle">点击打开地图选择具体位置</text>
          </view>
        </view>
        <view class="map-arrow">></view>
      </view>
    </view>

    <!-- 服务地址输入 -->
    <view class="form-section">
      <view class="form-item">
        <view class="form-label">服务地址</view>
        <input
          class="form-input"
          placeholder="{{addressPlaceholder || '请输入服务地址'}}"
          value="{{addressForm.address}}"
          bindinput="onAddressInput"
        />
      </view>

      <view class="form-item">
        <view class="form-label">详细地址 *</view>
        <input
          class="form-input"
          placeholder="{{detailPlaceholder || '请输入详细地址'}}"
          value="{{addressForm.addressDetail}}"
          bindinput="onAddressDetailInput"
          maxlength="255"
        />
        <view class="char-count">{{addressForm.addressDetail.length}}/255</view>
      </view>

      <view class="form-item">
        <view class="form-label">地址备注</view>
        <textarea
          class="form-textarea"
          placeholder="{{remarkPlaceholder || '如：小区东门'}}"
          value="{{addressForm.addressRemark}}"
          bindinput="onAddressRemarkInput"
          maxlength="255"
        ></textarea>
        <view class="char-count">{{addressForm.addressRemark.length}}/255</view>
      </view>

      <!-- 经纬度调试信息（可选显示） -->
      <view wx:if="{{showCoordinates && (addressForm.longitude || addressForm.latitude)}}" class="coordinate-info">
        <view class="coordinate-label">位置坐标</view>
        <view class="coordinate-text">
          经度: {{addressForm.longitude || '未设置'}} | 纬度: {{addressForm.latitude || '未设置'}}
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="closeModal">{{cancelText || '取消'}}</button>
      <button
        class="confirm-btn {{loading ? 'loading' : ''}}"
        bindtap="confirmModify"
        disabled="{{loading}}"
      >{{loading ? (loadingText || '修改中...') : (confirmText || '确认修改')}}</button>
    </view>
  </view>
</view>
