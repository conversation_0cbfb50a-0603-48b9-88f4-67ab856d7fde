<view class="time-modify-modal" wx:if="{{show}}">
  <view class="modal-mask" bindtap="close"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">{{title}}</text>
      <view class="close-btn" bindtap="close">
        <text class="close-icon">×</text>
      </view>
    </view>

    <view class="modal-body">
      <view class="form-item">
        <view class="form-label">服务时间</view>
        <view class="time-input" bindtap="showTimePickerModal">
          <text class="time-text {{timeForm.serviceTime ? '' : 'placeholder'}}">{{displayTime}}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>

      <view class="time-tip">
        <text class="tip-text">• 请至少预留足够时间</text>
        <text class="tip-text">• 修改时间可能需要重新安排服务人员</text>
      </view>
    </view>

    <view class="modal-footer">
      <view class="btn btn-cancel" bindtap="close">取消</view>
      <view class="btn btn-confirm" bindtap="confirm">确认修改</view>
    </view>
  </view>

  <!-- 时间选择器 -->
  <view class="time-picker-modal" wx:if="{{showTimePicker}}">
    <view class="picker-mask" bindtap="hideTimePickerModal"></view>
    <view class="picker-content">
      <view class="picker-header">
        <view class="picker-btn" bindtap="hideTimePickerModal">取消</view>
        <text class="picker-title">选择时间</text>
        <view class="picker-btn confirm" bindtap="confirmTimeSelection">确定</view>
      </view>
      <picker-view 
        class="picker-view" 
        value="{{timeArray}}" 
        bindchange="bindTimeChange"
        bindcolumnchange="bindTimeColumnChange">
        <picker-view-column>
          <view wx:for="{{timeRange[0]}}" wx:key="*this" class="picker-item">{{item}}年</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[1]}}" wx:key="*this" class="picker-item">{{item}}月</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[2]}}" wx:key="*this" class="picker-item">{{item}}日</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{timeRange[3]}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view>
