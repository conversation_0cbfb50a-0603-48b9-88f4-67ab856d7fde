/* pages/activity/detail.wxss */

/* 导航栏样式 */
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

.activity-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.detail-content {
  /* 不设置padding，让内容可以全屏显示 */
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.error-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background-color: #ff438f;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 活动详情 */
.activity-detail {
  background-color: #fff;
}

/* 活动头部 */
.activity-header {
  position: relative;
  width: 100%;
  height: 500rpx;
}

.header-image {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

.header-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
  color: #fff;
}

.activity-title {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 15rpx;
}

.activity-time {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 活动内容区域 */
.activity-content-wrapper {
  min-height: calc(100vh - 500rpx - 120rpx); /* 减去头部高度和导航栏高度 */
}

/* 富文本内容 */
.rich-text-content {
  padding: 30rpx;
  line-height: 1.6;
}

/* WebView内容 */
.webview-content {
  width: 100%;
  height: calc(100vh - 500rpx - 120rpx); /* 减去头部高度和导航栏高度 */
}

/* 无内容状态 */
.no-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 30rpx;
}

.no-content-text {
  color: #999;
  font-size: 28rpx;
}
