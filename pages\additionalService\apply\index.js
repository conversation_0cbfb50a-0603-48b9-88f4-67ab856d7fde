import additionalServiceApi from '../../../api/modules/additionalService.js';
import serviceApi from '../../../api/modules/service.js';
import orderApi from '../../../api/modules/order.js';
import rightsCardApi from '../../../api/modules/rightsCard.js';
import couponApi from '../../../api/modules/coupon.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    orderDetail: null,
    serviceId: null, // 主服务ID，用于获取增项服务

    // 可用服务列表
    availableServices: [],
    selectedServices: [], // 已选择的服务 [{serviceId, quantity, service}]
    
    // 优惠信息
    availableCards: [],
    availableCoupons: [],
    bestDiscountCard: null, // 最优折扣卡（自动应用）
    selectedCardId: '',
    selectedCouponId: '',
    
    // 价格计算
    originalPrice: 0,
    totalFee: 0,
    cardDeduction: 0,
    couponDeduction: 0,
    
    // 备注
    remark: '',
    
    // UI状态
    loading: false,
    showDiscountSelector: false,
    hasExistingApplication: false, // 是否已有申请记录
    existingServices: [], // 已申请的服务
    
    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: []
  },

  onLoad(options) {
    const { orderDetailId } = options;
    const userInfo = Session.getUser();
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId)
    });

    this.loadData();
  },

  /**
   * 加载页面数据
   */
  async loadData() {
    try {
      wx.showLoading({ title: '加载中...' });

      // 先加载订单详情获取服务ID
      await this.loadOrderDetail();

      // 然后并行加载可用服务、优惠信息和已有申请
      await Promise.all([
        this.loadAvailableServices(),
        this.loadAvailableDiscounts(),
        this.checkExistingApplications()
      ]);

    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    try {
      const { userInfo, orderDetailId } = this.data;

      // 这里需要通过orderDetailId获取对应的订单信息
      // 由于API设计，我们需要先获取订单列表，然后找到包含该orderDetailId的订单
      const orderList = await orderApi.getlists(userInfo.id, 'all');

      // 查找包含该orderDetailId的订单
      let targetOrder = null;
      let targetOrderDetail = null;

      for (const order of orderList || []) {
        const orderDetail = order.orderDetails?.find(detail => detail.id === orderDetailId);
        if (orderDetail) {
          targetOrder = order;
          targetOrderDetail = orderDetail;
          break;
        }
      }

      if (targetOrderDetail && targetOrderDetail.service) {
        this.setData({
          orderDetail: targetOrderDetail,
          serviceId: targetOrderDetail.service.id
        });
      } else {
        throw new Error('未找到对应的订单详情');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      throw error;
    }
  },

  /**
   * 加载可用服务
   */
  async loadAvailableServices() {
    try {
      const { serviceId } = this.data;

      if (!serviceId) {
        console.warn('服务ID不存在，无法加载增项服务');
        this.setData({ availableServices: [] });
        return;
      }

      // 使用现有的通过服务ID获取增项服务的接口
      const services = await serviceApi.getAdditionalService(serviceId);
      console.log('获取到的原始增项服务数据:', services);

      // 按照正常下单时的逻辑进行分组
      const dictionary = wx.getStorageSync('dictionary_com');
      const originalArray = services || [];
      const groupedArray = originalArray.reduce((acc, current) => {
        // 如果累加器中还没有这个type的数组，就创建一个
        if (!acc[current.type]) {
          acc[current.type] = {
            name: dictionary.find(v => v.code === current.type)?.name || current.type,
            id: current.type,
            children: [],
          };
        }
        // 将当前对象push到对应type的数组中
        acc[current.type].children.push(current);
        return acc;
      }, {});

      // 转换为数组
      const groupedList = Object.values(groupedArray);

      this.setData({
        availableServices: groupedList || []
      });

      // 更新服务选择状态
      this.updateServiceSelectionStatus();
    } catch (error) {
      console.error('加载可用服务失败:', error);
      throw error;
    }
  },

  /**
   * 加载可用优惠信息
   */
  async loadAvailableDiscounts() {
    try {
      const { userInfo, selectedServices, serviceId } = this.data;

      // 如果没有主服务ID，无法查询优惠信息
      if (!serviceId) {
        console.warn('主服务ID不存在，无法查询优惠信息');
        this.setData({
          availableCards: [],
          availableCoupons: []
        });
        return;
      }

      // 计算当前选择服务的总金额，用于查询可用的卡券
      // 如果还没有选择服务，使用默认金额1元来查询所有可能的优惠
      const totalAmount = selectedServices.reduce((total, item) => {
        const servicePrice = item.service.price || item.service.basePrice || item.service.servicePrice || 0;
        return total + (servicePrice * item.quantity);
      }, 0) || 1;

      const [cardsResult, couponsResult] = await Promise.all([
        rightsCardApi.getAvailableCards(userInfo.id, serviceId, totalAmount),
        couponApi.getAvailableCoupons(userInfo.id, serviceId, totalAmount)
      ]);

      // 过滤权益卡，只保留折扣卡，排除次卡（追加服务不支持次卡）
      let availableCards = [];
      let bestDiscountCard = null;
      if (cardsResult && cardsResult.list && cardsResult.list.length > 0) {
        availableCards = cardsResult.list.filter(card => {
          return card.cardType && card.cardType.type === 'discount';
        }).map(card => ({
          ...card,
          expiryTime: utils.formatNormalDate(card.expiryTime)
        }));

        // 找出最优的折扣卡（折扣力度最大的）
        if (availableCards.length > 0) {
          bestDiscountCard = availableCards.reduce((best, current) => {
            // 折扣率越小，优惠力度越大
            return best.cardType.discountRate < current.cardType.discountRate ? best : current;
          }, availableCards[0]);
        }
      }

      // 处理优惠券数据
      let availableCoupons = [];
      if (couponsResult && couponsResult.list && couponsResult.list.length > 0) {
        availableCoupons = couponsResult.list.map(coupon => ({
          ...coupon,
          threshold: Number(coupon.coupon.threshold || '0'),
          amount: Number(coupon.coupon.amount || '0'),
          expiryTime: utils.formatNormalDate(coupon.expiryTime)
        }));
      }

      this.setData({
        availableCards: [], // 不显示折扣卡选择，自动应用最优折扣卡
        availableCoupons,
        bestDiscountCard, // 保存最优折扣卡信息
        selectedCardId: bestDiscountCard ? bestDiscountCard.id : '' // 自动选择最优折扣卡
      });
    } catch (error) {
      console.error('加载优惠信息失败:', error);
      // 如果加载失败，设置为空数组，不影响其他功能
      this.setData({
        availableCards: [],
        availableCoupons: []
      });
    }
  },

  /**
   * 检查已有申请记录
   */
  async checkExistingApplications() {
    try {
      const { orderDetailId } = this.data;

      // 查询该订单详情下的追加服务申请
      const applications = await additionalServiceApi.list(orderDetailId);

      if (applications && applications.length > 0) {
        // 查找进行中的申请（排除已拒绝、已取消、已退款的）
        const activeApps = applications.filter(app =>
          app.status === 'pending_confirm' ||
          app.status === 'confirmed' ||
          app.status === 'pending_payment' ||
          app.status === 'paid' ||
          app.status === 'completed' ||
          app.status === 'refunding'
        );

        if (activeApps.length > 0) {
          // 有进行中的申请，回填数据并禁用提交
          const latestApp = activeApps[0]; // 取最新的申请

          // 构建已选服务数据
          const existingServices = latestApp.details.map(detail => ({
            serviceId: detail.serviceId,
            quantity: detail.quantity,
            service: {
              id: detail.serviceId,
              name: detail.serviceName,
              price: detail.servicePrice || detail.price || 0
            }
          }));

          // 回填优惠券选择（如果有的话）
          let selectedCouponId = '';
          if (latestApp.discountInfos && latestApp.discountInfos.length > 0) {
            const couponDiscount = latestApp.discountInfos.find(info => info.discountType === 'coupon');
            if (couponDiscount) {
              selectedCouponId = couponDiscount.discountId;
            }
          }

          this.setData({
            hasExistingApplication: true,
            existingServices,
            selectedServices: existingServices,
            selectedCouponId,
            remark: latestApp.remark || '',
            // 直接使用后端返回的价格信息，不重新计算
            originalPrice: (latestApp.originalPrice || 0).toFixed(2),
            cardDeduction: (latestApp.cardDeduction || 0).toFixed(2),
            couponDeduction: (latestApp.couponDeduction || 0).toFixed(2),
            totalFee: (latestApp.totalFee || 0).toFixed(2)
          });

          // 更新服务选择状态
          this.updateServiceSelectionStatus();

          // 注意：这里不调用 calculatePrice()，因为我们直接使用后端返回的价格数据
        }
      }
    } catch (error) {
      console.error('检查已有申请失败:', error);
      // 检查失败不影响正常流程
    }
  },

  /**
   * 选择服务
   */
  async selectService(e) {
    const { service } = e.currentTarget.dataset;
    const { selectedServices, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改',
        icon: 'none'
      });
      return;
    }

    // 检查是否已选择
    const existingIndex = selectedServices.findIndex(item => item.serviceId === service.id);

    if (existingIndex >= 0) {
      // 已选择，增加数量
      const updatedServices = [...selectedServices];
      updatedServices[existingIndex].quantity += 1;
      this.setData({ selectedServices: updatedServices });
    } else {
      // 未选择，添加到列表
      // 标准化服务对象，确保价格字段一致
      const standardizedService = {
        id: service.id,
        name: service.name,
        price: service.price || service.basePrice || service.servicePrice || 0
      };

      const newService = {
        serviceId: service.id,
        quantity: 1,
        service: standardizedService
      };
      this.setData({
        selectedServices: [...selectedServices, newService]
      });
    }

    this.calculatePrice();

    // 更新服务选择状态显示
    this.updateServiceSelectionStatus();

    // 重新加载优惠信息（基于新的服务金额）
    await this.loadAvailableDiscounts();

    // 选择服务后自动滚动到已选服务区域
    this.scrollToSelectedServices();
  },

  /**
   * 更新服务选择状态显示
   */
  updateServiceSelectionStatus() {
    const { availableServices, selectedServices } = this.data;

    // 为每个服务添加选择状态
    const updatedServices = availableServices.map(group => ({
      ...group,
      children: group.children.map(service => {
        const selectedService = selectedServices.find(item => item.serviceId === service.id);
        return {
          ...service,
          isSelected: !!selectedService,
          selectedQuantity: selectedService ? selectedService.quantity : 0
        };
      })
    }));

    this.setData({
      availableServices: updatedServices
    });
  },

  /**
   * 滚动到已选服务区域
   */
  scrollToSelectedServices() {
    // 延迟执行，确保DOM更新完成
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('#selected-services-section')
        .boundingClientRect((rect) => {
          if (rect) {
            wx.pageScrollTo({
              scrollTop: rect.top + wx.getSystemInfoSync().scrollTop - 100, // 留一些边距
              duration: 300
            });
          }
        })
        .exec();
    }, 100);
  },

  /**
   * 调整服务数量
   */
  async adjustServiceQuantity(e) {
    const { serviceId, action } = e.currentTarget.dataset;
    const { selectedServices, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用调整
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改',
        icon: 'none'
      });
      return;
    }

    const updatedServices = selectedServices.map(item => {
      if (item.serviceId === serviceId) {
        const newQuantity = action === 'increase' ? item.quantity + 1 : item.quantity - 1;
        return { ...item, quantity: Math.max(0, newQuantity) };
      }
      return item;
    }).filter(item => item.quantity > 0); // 移除数量为0的服务

    this.setData({ selectedServices: updatedServices });
    this.calculatePrice();

    // 更新服务选择状态显示
    this.updateServiceSelectionStatus();

    // 重新加载优惠信息（基于新的服务金额）
    await this.loadAvailableDiscounts();
  },

  /**
   * 计算价格
   */
  calculatePrice() {
    const { selectedServices, selectedCouponId, availableCoupons, bestDiscountCard } = this.data;

    // 计算原价
    const originalPrice = selectedServices.reduce((total, item) => {
      // 尝试多种可能的价格字段名称
      const servicePrice = item.service.price || item.service.basePrice || item.service.servicePrice || 0;
      return total + (servicePrice * item.quantity);
    }, 0);

    // 计算权益卡抵扣（自动应用最优折扣卡）
    let cardDeduction = 0;
    if (bestDiscountCard && bestDiscountCard.cardType && bestDiscountCard.cardType.type === 'discount') {
      // 折扣率是小数形式，如0.8表示8折，所以抵扣金额 = 原价 * (1 - 折扣率)
      cardDeduction = originalPrice * (1 - bestDiscountCard.cardType.discountRate);
    }
    
    // 计算优惠券抵扣
    let couponDeduction = 0;
    if (selectedCouponId) {
      const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
      if (selectedCoupon) {
        couponDeduction = Math.min(selectedCoupon.amount, originalPrice - cardDeduction);
      }
    }
    
    const totalFee = Math.max(0, originalPrice - cardDeduction - couponDeduction);
    
    this.setData({
      originalPrice: originalPrice.toFixed(2),
      cardDeduction: cardDeduction.toFixed(2),
      couponDeduction: couponDeduction.toFixed(2),
      totalFee: totalFee.toFixed(2)
    });
  },

  /**
   * 显示优惠选择器
   */
  showDiscountSelector() {
    const { hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改优惠券',
        icon: 'none'
      });
      return;
    }

    this.setData({ showDiscountSelector: true });
  },

  /**
   * 隐藏优惠选择器
   */
  hideDiscountSelector() {
    this.setData({ showDiscountSelector: false });
  },



  /**
   * 选择优惠券
   */
  selectCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    const { selectedCouponId, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用选择
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法修改优惠券',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedCouponId: selectedCouponId === couponId ? '' : couponId
    });

    this.calculatePrice();
  },

  /**
   * 备注输入
   */
  onRemarkInput(e) {
    this.setData({ remark: e.detail.value });
  },

  /**
   * 提交申请
   */
  async submitApplication() {
    const { selectedServices, userInfo, orderDetailId, selectedCouponId, remark, bestDiscountCard, availableCoupons, hasExistingApplication } = this.data;

    // 如果已有申请记录，禁用提交
    if (hasExistingApplication) {
      wx.showToast({
        title: '已有申请记录，无法重复提交',
        icon: 'none'
      });
      return;
    }

    if (selectedServices.length === 0) {
      wx.showToast({
        title: '请选择要追加的服务',
        icon: 'none'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '提交中...' });
      
      // 构建请求数据
      const requestData = {
        customerId: userInfo.id,
        services: selectedServices.map(item => ({
          serviceId: item.serviceId,
          quantity: item.quantity
        })),
        remark
      };
      
      // 添加优惠信息
      const discountInfos = [];
      if (bestDiscountCard) {
        discountInfos.push({
          discountType: 'membership_card',
          discountId: bestDiscountCard.id,
          discountAmount: parseFloat(this.data.cardDeduction)
        });
      }
      
      if (selectedCouponId) {
        const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
        if (selectedCoupon) {
          discountInfos.push({
            discountType: 'coupon',
            discountId: selectedCouponId,
            discountAmount: parseFloat(this.data.couponDeduction)
          });
        }
      }
      
      if (discountInfos.length > 0) {
        requestData.discountInfos = discountInfos;
      }
      
      // 提交申请
      const result = await additionalServiceApi.create(orderDetailId, requestData);
      console.log('追加服务申请提交结果:', result);

      wx.hideLoading();

      // 检查返回结果，如果为null说明有错误
      if (result === null) {
        // analysisRes已经显示了错误toast，这里不需要再显示
        return;
      }

      // 显示成功提示
      this.setData({
        showModal: true,
        modalTitle: '申请成功',
        modalContent: '您的追加服务申请已提交，请等待员工确认',
        modalButtons: [
          {
            text: '确定',
            type: 'primary',
            event: 'handleModalConfirm'
          }
        ]
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });

    // 返回到订单详情页面，添加小延迟确保后端处理完成
    setTimeout(() => {
      wx.navigateBack();
    }, 500);
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },

  /**
   * 查看申请记录 - 返回到订单详情页面查看追加服务
   */
  viewApplicationList() {
    // 直接返回到订单详情页面，用户可以在那里查看追加服务状态
    wx.navigateBack();
  }
});
