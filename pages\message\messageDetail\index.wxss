/* pages/message/messageDetail/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 24rpx;
  background: url('https://xian7.zos.ctyun.cn/pet/static/bj.png') no-repeat;
  background-size: contain;
}

.content {
  background-color: white;
  border-radius: 24rpx;
  min-height: calc(100vh - 200rpx);
  overflow: hidden;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
}

.error-container {
  padding: 100rpx 24rpx;
}

.message-detail {
  padding: 24rpx;
}

/* 消息头部 */
.message-header {
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.message-header.unread {
  background-color: #f8f9ff;
  border-left: 4rpx solid #007aff;
}

.message-header.read {
  background-color: #f8f9fa;
  border-left: 4rpx solid #28a745;
}

.message-info {
  display: flex;
  align-items: flex-start;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.message-meta {
  flex: 1;
}

.message-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.read-status {
  font-size: 22rpx;
  color: #28a745;
  background-color: #d4edda;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  display: inline-block;
}

/* 消息内容 */
.message-content {
  background-color: #fff;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #eee;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
}

/* 额外数据 */
.message-extra {
  background-color: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.extra-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.extra-item {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.extra-item:last-child {
  margin-bottom: 0;
}

.extra-label {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.extra-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-wrap: break-word;
}
