import request, { analysisRes } from '../request';
import config from '../config';
import { OrderStatus } from '../../common/constant';

const { apiUrls, debug } = config;
const { order } = apiUrls;

export default {
  // 新增订单
  async add(useId, data) {
    const res = await request.post(order.add.replace('{id}', useId), data);
    return analysisRes(res);
  },

  // 获取订单状态
  async getOrderStatus(customerId, sn) {
    try {
      const res = await request.get(order.status.replace('{customerId}', customerId).replace('{sn}', sn));
      return analysisRes(res);
    } catch (error) {
      console.error('获取订单状态失败:', error);
      return null;
    }
  },

  // 支付订单
  async pay(customerId, sn) {
    try {
      // 先检查订单状态
      const status = await this.getOrderStatus(customerId, sn);
      if (status !== OrderStatus.待付款) {
        return {
          success: false,
          error: '订单状态不正确',
        };
      }

      const res = await request.post(order.pay.replace('{customerId}', customerId).replace('{sn}', sn));
      const result = analysisRes(res);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('支付订单失败:', error);
      return {
        success: false,
        error: error.message || '支付失败，请稍后重试',
      };
    }
  },

  // refundRequest: "/customers/{customerId}/refundRequest/{sn}", // 申请退款
  async refundRequest(customerId, sn) {
    const res = await request.post(order.refundRequest.replace('{customerId}', customerId).replace('{sn}', sn));
    return analysisRes(res);
  },

  // 查询订单列表
  async getlists(id, status) {
    const res = await request.get(order.list.replace('{id}', id), { status: status === 'all' ? '' : status });
    return analysisRes(res);
  },

  // detail: "/customers//{id}/order/{orderId}", // 获取订单详情
  async getDetail(customerId, orderId) {
    const res = await request.get(order.detail.replace('{id}', customerId).replace('{orderId}', orderId));
    return analysisRes(res);
  },

  // 取消订单
  async cancel(customerId, orderId) {
    const res = await request.delete(order.cancel.replace('{customerId}', customerId).replace('{orderId}', orderId));
    return analysisRes(res);
  },

  // 处理0元订单
  async handleZeroAmountOrder(customerId, sn) {
    try {
      // 先检查订单状态
      const status = await this.getOrderStatus(customerId, sn);
      if (status !== OrderStatus.待付款) {
        return {
          success: false,
          error: '订单状态不正确',
        };
      }

      // 更新订单状态
      const result = await this.pay(customerId, sn);
      return result;
    } catch (error) {
      console.error('处理0元订单失败:', error);
      return {
        success: false,
        error: error.message || '处理失败，请稍后重试',
      };
    }
  },

  // 查询订单服务照片
  async getServicePhotos(orderId) {
    try {
      const res = await request.get(order.servicePhotos.replace('{orderId}', orderId));
      return analysisRes(res);
    } catch (error) {
      console.error('获取服务照片失败:', error);
      // 如果是404错误，说明没有照片，返回null
      if (error.statusCode === 404) {
        return null;
      }
      return null;
    }
  },

  // 修改订单服务地址
  async updateServiceAddress(orderId, data) {
    try {
      const res = await request.put(order.updateServiceAddress.replace('{orderId}', orderId), data);
      return analysisRes(res);
    } catch (error) {
      console.error('修改服务地址失败:', error);
      throw error;
    }
  },

  // 修改订单服务时间
  async updateServiceTime(orderId, serviceTime) {
    try {
      const res = await request.put(order.updateServiceTime.replace('{orderId}', orderId), {
      userType: 'customer',
      serviceTime,
    });
      return analysisRes(res);
    } catch (error) {
      console.error('修改服务时间失败:', error);
      throw error;
    }
  },
};
