/* 地址修改弹窗样式 */
.address-modify-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地址类型选择器 */
.address-type-selector {
  padding: 30rpx 40rpx 20rpx;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.type-buttons {
  display: flex;
  gap: 20rpx;
}

.type-btn {
  flex: 1;
  height: 65rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
}

.type-btn.active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

/* 已保存地址列表 */
.saved-address-section {
  padding: 0 40rpx 20rpx;
}

.saved-address-list {
  max-height: 400rpx;
}

/* 地址卡片样式 - 参考系统地址列表 */
.address-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.address-card:last-child {
  margin-bottom: 0;
}

.address-card.selected {
  border-color: #007aff;
  background-color: rgba(0, 122, 255, 0.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.address-main-info {
  flex: 1;
  padding-right: 20rpx;
}

.address-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.address-detail-text {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.3;
}

.address-tags {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.default-badge {
  background-color: #ff4391;
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.coordinate-badge {
  background-color: #52c41a;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  font-weight: 500;
  margin-top: 4rpx;
}

.selected-badge {
  background-color: #007aff;
  color: #fff;
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.selected-icon {
  font-size: 20rpx;
  font-weight: bold;
}

/* 空状态样式 */
.empty-address-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 地图选择区域 */
.map-select-section {
  padding: 0 40rpx 20rpx;
}

.map-select-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx dashed #007aff;
  transition: all 0.3s ease;
}

.map-select-card:active {
  background-color: rgba(0, 122, 255, 0.05);
  transform: scale(0.98);
}

.map-select-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.map-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.map-icon {
  font-size: 32rpx;
}

.map-text-wrapper {
  flex: 1;
}

.map-title {
  font-size: 30rpx;
  color: #007aff;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.map-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.map-arrow {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

/* 表单区域 */
.form-section {
  padding: 20rpx 40rpx;
  flex: 1;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  right: 20rpx;
  bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* 经纬度信息显示 */
.coordinate-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.coordinate-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coordinate-text {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  background-color: #fff;
  color: #666;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 1;
  border: none;
  border-radius: 40rpx;
  background-color: #007aff;
  color: #fff;
  font-size: 28rpx;
}

.confirm-btn.loading {
  background-color: #ccc;
  color: #999;
}
