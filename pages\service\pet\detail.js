import user from '../../../api/modules/user';
import orderApi from '../../../api/modules/order';
const utils = require('../../utils/util');
import siteinfo from "../../../siteinfo";

Page({
  data: {
    userInfo: null,
    siteinfo,
    petInfo: null,
    lastServiceData: null,
    recentOrders: [],
    loading: true,
    petId: null,
  },

  onLoad(options) {
    // 使用页面扩展的用户信息获取方式
    const userInfo = this.data.userInfo || this.$session?.getUser();
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const { petId } = options;
    if (!petId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      petId: parseInt(petId)
    });

    this.loadPetDetail();
  },

  /**
   * 加载宠物详情
   */
  async loadPetDetail() {
    try {
      wx.showLoading({ title: '加载中...' });

      const { userInfo, petId } = this.data;

      // 并行加载宠物信息和最后洗护时间
      const [pets, lastServiceData] = await Promise.all([
        user.getPets(userInfo.id),
        this.getLastServiceTime(userInfo.id, petId)
      ]);

      // 找到当前宠物信息
      const petInfo = pets.find(pet => pet.id === petId);
      if (!petInfo) {
        wx.showToast({
          title: '宠物信息不存在',
          icon: 'none'
        });
        wx.navigateBack();
        return;
      }

      // 格式化宠物年龄
      if (typeof petInfo.bri === 'number' && !isNaN(petInfo.bri)) {
        petInfo.formattedBri = utils.formatAge(petInfo.bri);
      } else {
        petInfo.formattedBri = '无效年龄';
      }

      // 格式化生日
      if (petInfo.birthday) {
        petInfo.formattedBirthday = utils.formatDateTime(petInfo.birthday, 'YYYY-MM-DD');
      }

      this.setData({
        petInfo,
        lastServiceData,
        loading: false
      });

      // 加载最近的订单记录
      this.loadRecentOrders();

    } catch (error) {
      console.error('加载宠物详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 获取最后洗护时间
   */
  async getLastServiceTime(userId, petId) {
    try {
      const data = await user.getLastServiceTime(userId, petId);
      if (data.lastServiceTime) {
        data.formattedLastServiceTime = utils.formatLastServiceTime(data.lastServiceTime);
        data.formattedServiceTime = data.serviceTime ? utils.formatNormalDate(data.serviceTime) : '';
      }
      return data;
    } catch (error) {
      return {
        lastServiceTime: null,
        formattedLastServiceTime: '暂无洗护记录'
      };
    }
  },

  /**
   * 加载最近的订单记录
   */
  async loadRecentOrders() {
    try {
      const { userInfo, petId } = this.data;

      // 获取用户所有订单
      const orders = await orderApi.getlists(userInfo.id, 'all');

      // 筛选出该宠物的订单，并按时间排序
      const petOrders = orders
        .filter(order => order.petId === petId)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5) // 只显示最近5条
        .map(order => ({
          ...order,
          formattedCreatedAt: utils.formatNormalDate(order.createdAt),
          formattedServiceTime: order.serviceTime ? utils.formatNormalDate(order.serviceTime) : '',
          formattedTotalFee: (order.totalFee * 1).toFixed(2)
        }));

      // 判断是否有足够的订单数据来显示订单列表
      // 如果订单数量大于等于3条，显示订单列表；否则显示最后洗护时间
      const hasOrderHistory = petOrders.length >= 3;

      this.setData({
        recentOrders: petOrders,
        hasOrderHistory: hasOrderHistory
      });

    } catch (error) {
      console.error('加载订单记录失败:', error);
      // 设置为无订单历史状态
      this.setData({
        recentOrders: [],
        hasOrderHistory: false
      });
    }
  },

  /**
   * 编辑宠物信息
   */
  editPet() {
    const { petInfo } = this.data;
    wx.setStorageSync('editPetItem', petInfo);
    wx.navigateTo({
      url: '/pages/service/pet/addPet'
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const { orderId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/serviceOrder/orderDetail/index?orderId=${orderId}`
    });
  },

  /**
   * 预览头像
   */
  previewAvatar() {
    const { petInfo } = this.data;
    if (petInfo.avatar) {
      wx.previewImage({
        current: petInfo.avatar,
        urls: [petInfo.avatar]
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadPetDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { petInfo } = this.data;
    return {
      title: `我的爱宠 ${petInfo?.name || ''}`,
      path: `/pages/service/pet/detail?petId=${this.data.petId}`,
      imageUrl: petInfo?.avatar || ''
    };
  }
});
