<view class="container">
  <!-- 导航栏 -->
  <diy-navbar :isFixed="true" CustomBar='60' bgCustom="#fff">
    <view slot="content">
      <view class="flex align-center flex-nowrap diygw-col-24">
        <view class="flex-sub text-center">
          <text class="text-lg text-bold">页面详情</text>
        </view>
        <view class="flex-none" bind:tap="goBack">
          <text class="cuIcon-back text-xl"></text>
        </view>
      </view>
    </view>
  </diy-navbar>

  <!-- WebView内容 -->
  <view wx:if="{{!showError && webviewUrl}}" class="webview-container">
    <web-view 
      src="{{webviewUrl}}" 
      binderror="onWebViewError"
    ></web-view>
  </view>

  <!-- 错误页面 -->
  <view wx:if="{{showError}}" class="error-container">
    <view class="error-content">
      <view class="error-icon">
        <text class="cuIcon-roundclosefill text-red text-xxl"></text>
      </view>
      <view class="error-message">
        <text class="text-lg text-gray">{{errorMessage}}</text>
      </view>
      <view class="error-actions">
        <button class="cu-btn bg-blue margin-tb-sm" bind:tap="reload">
          重新加载
        </button>
        <button class="cu-btn line-blue margin-tb-sm" bind:tap="goBack">
          返回上页
        </button>
      </view>
    </view>
  </view>

  <!-- 加载中状态 -->
  <view wx:if="{{!showError && !webviewUrl}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-icon">
        <text class="cuIcon-loading2 text-blue text-xl"></text>
      </view>
      <view class="loading-text">
        <text class="text-gray">页面加载中...</text>
      </view>
    </view>
  </view>
</view>
