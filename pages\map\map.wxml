<!--pages/map/map.wxml-->
<view class="container">
  <view class="map-container">
    <map id="map" longitude="{{longitude}}" latitude="{{latitude}}" scale="16" markers="{{markers}}" bind:markertap="markertap" bind:regionchange="regionchange" show-location style="width: 100%; height: 60vh;">
    </map>
    <view class="map-actions">
      <view class="action-icon" bind:tap="moveToLocation">
        <text class="flex icon6 diygw-col-0 diy-icon-locationfill"></text>
      </view>
    </view>
  </view>

  <!-- 显示重试按钮 -->
  <view class="retry-container" wx:if="{{showRetry}}">
    <view class="retry-content">
      <text class="retry-text">获取位置失败，请重试</text>
      <button class="retry-button" bind:tap="updateLocation">重新获取位置</button>
    </view>
  </view>

  <!-- 车辆列表 -->
  <view class="vehicle-container" wx:if="{{vehicles.length > 0}}">
    <view class="list-title-container">
      <view class="list-title">附近可用车辆</view>
      <image src="//xian7.zos.ctyun.cn/pet/static/refresh.png"
             class="refresh-icon"
             bind:tap="moveToLocation" />
    </view>
    <scroll-view scroll-y style="height: fit-content;">
      <view class="vehicle-item {{index === selectedIndex ? 'selected' : ''}}" wx:for="{{vehicles}}" wx:key="id"
            data-index="{{index}}" bind:tap="onVehicleTap">
        <view class="vehicle-info">
          <text class="plate-number">车牌：{{item.plateNumber || '未知车牌'}}</text>
          <view>
            <text wx:if="{{item.isNearest}}" class="nearest-tag">最近</text>
            <text class="vehicle-status {{item.status === '空闲' ? 'available' : 'busy'}}">{{item.status}}</text>
          </view>
        </view>
        <view class="distance-info">
          距离: {{item.distanceText || '未知距离'}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 无车辆时的提示 -->
  <view class="no-vehicle-container" wx:if="{{!showRetry && vehicles.length === 0 && latitude && longitude}}">
    <view class="no-vehicle-content">
      <text class="no-vehicle-text">附近暂无可用车辆</text>
      <button class="refresh-button" bind:tap="findNearbyVehicles">刷新</button>
    </view>
  </view>
</view>