import request, { analysisRes } from "../request";
import config from "../config";

const { activity } = config.apiUrls;

export default {
  // 获取当前发布的活动
  async getCurrent(params = {}) {
    const res = await request.get(activity.current, params);
    return analysisRes(res);
  },

  // 获取活动详情
  async getDetail(id) {
    const res = await request.get(activity.detail.replace("{id}", id));
    return analysisRes(res);
  },
};
