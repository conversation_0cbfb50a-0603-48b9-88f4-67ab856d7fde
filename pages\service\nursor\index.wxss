/* pages/service/nursor/index.wxss */
.nursor-page {
  background-color: rgba(246, 246, 246, 1);
  height: 100vh;
  position:relative;
}
.nursor-page .nursor-bg image{
  width: 100%;
}
.nursor-list{
  margin-top: -60rpx;
  padding: 0 40rpx;
}
/* 员工卡片样式 */
.nursor-list-item {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.employee-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  gap: 20rpx;
}

/* 头像区域 */
.employee-avatar-section {
  flex-shrink: 0;
}

.employee-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 2rpx solid #f0f0f0;
}

/* 信息区域 */
.employee-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 姓名行 */
.employee-name-row {
  display: flex;
  align-items: center;
}

.employee-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 标签行 */
.employee-tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  line-height: 1;
}

.position-tag {
  background-color: rgba(52, 199, 89, 0.1);
  color: rgba(52, 199, 89, 1);
}

.level-tag {
  background-color: rgba(255, 67, 143, 0.1);
  color: rgba(255, 67, 143, 1);
}

.exp-tag {
  background-color: rgba(0, 122, 255, 0.1);
  color: rgba(0, 122, 255, 1);
}

/* 评分行 */
.employee-rating-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #666;
}

/* 车辆信息行 */
.employee-vehicle-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.vehicle-info {
  font-size: 24rpx;
  color: #666;
}

.vehicle-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.status-free {
  background-color: rgba(52, 199, 89, 0.1);
  color: rgba(52, 199, 89, 1);
}

.status-busy {
  background-color: rgba(255, 149, 0, 0.1);
  color: rgba(255, 149, 0, 1);
}

/* 选择按钮区域 */
.employee-action-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.select-btn {
  background-color: rgba(255, 67, 143, 1);
  padding: 16rpx 32rpx;
  border-radius: 48rpx;
  cursor: pointer;
}

.select-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 新增样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 旧样式已移除，使用新的员工卡片样式 */

.load-more, .no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  color: #999;
  font-size: 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}