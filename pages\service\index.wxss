.servicepage {
  background-color: rgba(246, 246, 246, 1);
  height: calc(100vh - 160rpx);
  position: relative;
}
.choose-pet{
  padding: 8rpx 20rpx;
  width: 100%;
}
.choose-pet .add-pet{
  background: white;
  padding: 16rpx 40rpx 16rpx 16rpx;
  line-height: 70rpx;
  border-radius: 20rpx;
  border: 1px solid #dadada;
  position: relative;
}
.choose-pet .diy-icon-add{
  padding: 0 20rpx;
  border: 1px solid #eee;
  text-align: center;
  border-radius: 16rpx;
  margin-right: 20rpx;
}
.choose-pet>image{
  width: 180rpx;
  height: 148rpx !important;
}
.image7-clz {
  border-radius: 24rpx;
  overflow: hidden;
}

.image7-size {
  height: 90rpx !important;
  width: 90rpx !important;
}
.text23-clz{
  font-weight: bold;
  padding: 0 24rpx;
}

.exchangeIcon {
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
  background-color: #2F83FF;
  border-radius: 50%;
  position: absolute;
  right: -5%;
}
.serviceTab {
  height: calc(100vh - 270rpx);
  overflow-y: auto;
}

.serviceTab .diygw-tab-item {
  height: 120rpx;
  line-height: 50rpx;
  padding: 10rpx 0;
  position: relative;
  flex-direction: column;
}

.serviceTab .type {
  display: block;
}

.serviceTab .diy-icon-titles {
  display: none;
  position: absolute;
  top: 40rpx;
  left: 0;
  color: rgba(255, 67, 145, 1);
}

.serviceTab .current {
  background-color: white;
  font-weight: bold;
}

.serviceTab .current .diy-icon-titles {
  display: block;
}

.tab-content {
  background-color: white;
  width: calc(100vw - 200rpx);
  height: 100%;
  overflow-y: auto;
  padding: 20rpx 20rpx 180rpx;
}

.tab-content-title {
  font-family: Roboto;
  font-weight: 700;
  font-size: 32rpx;
  line-height: 100%;
  letter-spacing: 0%;
  text-transform: uppercase;
  line-height: 80rpx;
}
.flex-card{
  border: 1px solid rgba(238, 238, 238, 1);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.image-size {
  border-radius: 16rpx;
  overflow: hidden;
  height: 160rpx !important;
  width: 160rpx !important;
}
.right-content{
  padding-left: 20rpx;
}
.text-bold {
  font-size: 30rpx;
  line-height: 60rpx;
}
.item-price-wrap{
  color:rgba(255, 67, 145, 1);
}
.text-price{
  padding: 0 8rpx;
  font-size: 40rpx;
  font-weight: bold;
}
.diy-icon-roundright{
  color: rgba(47, 131, 255, 1);
  font-size: 40rpx;
}

/* 员工提示区域样式 - 简化版 */
.employee-notice-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin: 20rpx;
  margin-bottom: 0;
}

.employee-notice-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.notice-icon {
  width: 28rpx;
  height: 28rpx;
}

.notice-text {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
}

.notice-desc {
  color: #666;
  font-size: 22rpx;
  margin-left: 8rpx;
}

.notice-close {
  padding: 8rpx 16rpx;
  background: #fff;
  border: 1rpx solid #ddd;
  border-radius: 20rpx;
}

.close-text {
  color: #666;
  font-size: 22rpx;
}
