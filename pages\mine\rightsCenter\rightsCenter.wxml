<view class="right-container">
  <view class='right-header'>
    <image src='https://xian7.zos.ctyun.cn/pet/static/centerbj.png' mode="scaleToFill" />
    <image src='https://xian7.zos.ctyun.cn/pet/static/bgImage.png' mode="aspectFit" />
  </view>
  <view class="right-content">
    <view wx:if="{{hasRights_nk || hasRights_ck || hasCoupons}}">
      <view wx:if="{{hasRights_nk}}" class="con1">
        <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
          <text class="diygw-col-8 text-clz"> </text>
          <text class="diygw-col-8 text16-clz"> 年卡福利 </text>
          <text class="diygw-col-8 text-clz"> </text>
        </view>
        <view wx:for="{{rightsList_nk}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="flex flex-wrap diygw-col-24 item item{{index%3+1}} flex-clz" >
          <view class="flex flex-wrap diygw-col-18 flex-direction-column justify-center padding-left flex1-clz">
            <view class="flex flex-wrap diygw-col-0 justify-start items-baseline flex2-clz">
              <text class="diygw-col-0 text2-clz"> ¥ </text>
              <text class="diygw-col-0 text3-clz"> {{item.price || 0}} </text>
            </view>
            <text class="diygw-col-0"> {{item.description || ''}} </text>
          </view>
          <view class="flex flex-wrap diygw-col-6 items-center justify-center flex3-clz" bindtap="buyItem" data-id="{{item.id}}" data-type="rightsCard">
            <text class="diygw-col-0 text7-clz"> 立即购买 </text>
          </view>
          <text class="diygw-col-24 text8-clz"> </text>
          <text class="diygw-col-24 text9-clz"> </text>
        </view>
      </view>
      <view wx:if="{{hasRights_ck}}" class="con2">
        <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
          <text class="diygw-col-8 text-clz"> </text>
          <text class="diygw-col-8 text16-clz"> 次卡 </text>
          <text class="diygw-col-8 text-clz"> </text>
        </view>
        <view wx:for="{{rightsList_ck}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="flex flex-wrap diygw-col-24 item item{{index%3+1}} flex-clz">
          <view class="flex flex-wrap diygw-col-18 flex-direction-column justify-center padding-left flex1-clz">
            <view class="flex flex-wrap diygw-col-0 justify-start items-baseline flex2-clz">
              <text class="diygw-col-0 text2-clz"> ¥ </text>
              <text class="diygw-col-0 text3-clz"> {{item.price || 0}} </text>
            </view>
            <text class="diygw-col-0"> {{item.description || ''}} </text>
          </view>
          <view class="flex flex-wrap diygw-col-6 items-center justify-center flex3-clz" bindtap="buyItem" data-id="{{item.id}}" data-type="rightsCard">
            <text class="diygw-col-0 text7-clz"> 立即购买 </text>
          </view>
          <text class="diygw-col-24 text8-clz"> </text>
          <text class="diygw-col-24 text9-clz"> </text>
        </view>
      </view>
      <view wx:if="{{hasCoupons}}" class="con3">
        <view class="flex flex-wrap diygw-col-24 justify-center items-center flex22-clz">
          <text class="diygw-col-8 text-clz"> </text>
          <text class="diygw-col-8 text16-clz"> 优惠券 </text>
          <text class="diygw-col-8 text-clz"> </text>
        </view>
        <view wx:for="{{couponsList}}" wx:for-index="index" wx:key="id" wx:for-item="item" class="flex flex-wrap diygw-col-24 item item{{index%3+1}} flex-clz">
          <view class="flex flex-wrap diygw-col-18 flex-direction-column justify-center padding-left flex1-clz">
            <view class="flex flex-wrap diygw-col-0 justify-start items-baseline flex2-clz">
              <text class="diygw-col-0 text2-clz"> ¥ </text>
              <text class="diygw-col-0 text3-clz"> {{item.amount || 0}} </text>
            </view>
            <text class="diygw-col-0"> {{item.description || ''}} </text>
          </view>
          <view class="flex flex-wrap diygw-col-6 items-center justify-center flex3-clz" bindtap="buyItem" data-id="{{item.id}}" data-type="coupon">
            <text wx:if="{{item.price === 0}}" class="diygw-col-0 text7-clz"> 免费领取 </text>
            <text wx:else class="diygw-col-0 text7-clz"> 立即购买 </text>
          </view>
          <text class="diygw-col-24 text8-clz"> </text>
          <text class="diygw-col-24 text9-clz"> </text>
        </view>
      </view>
    </view>
    <view wx:else class="flex flex-direction-column justify-center items-center  diygw-col-24 flex22-clz empty">
      <image src="https://xian7.zos.ctyun.cn/pet/static/pet.png" style="width: 300rpx; height: 250rpx;" ></image>
      <view>敬请期待</view>
    </view>
  </view>
  <!-- 自定义模态框 -->
  <custom-modal show="{{showModal}}" bind:confirm="onModalConfirm" title="{{modalTitle}}" content="{{modalContent}}"></custom-modal>
</view>