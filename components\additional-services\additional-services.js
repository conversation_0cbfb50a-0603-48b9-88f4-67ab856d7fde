Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 追加服务列表
    additionalServices: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 查看追加服务详情
     */
    onViewDetail(e) {
      const { id } = e.currentTarget.dataset;
      
      this.triggerEvent('viewDetail', {
        id: parseInt(id)
      });
    },

    /**
     * 支付追加服务
     */
    onPayService(e) {
      const { id } = e.currentTarget.dataset;
      
      this.triggerEvent('payService', {
        id: parseInt(id)
      });
    },

    /**
     * 删除追加服务
     */
    onDeleteService(e) {
      const { id, name } = e.currentTarget.dataset;
      
      this.triggerEvent('deleteService', {
        id: parseInt(id),
        name: name
      });
    }
  }
});
