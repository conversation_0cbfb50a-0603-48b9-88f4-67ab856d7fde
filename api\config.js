export default {
  debug: true,
  // 基础配置
  baseUrl: "http://192.168.1.20:3001",
  // baseUrl: "https://manager.petsjoylife.com/api",
  // baseUrl: "https://test.xdpb.top/api",
  timeout: 10000,
  zos_endpoint: "xian7.zos.ctyun.cn",
  zos_bucket: "pet",
  // WebSocket配置
  socketUrl: "ws://192.168.1.20:3001",
  // socketUrl: "wss://manager.petsjoylife.com/ws/",

  // API路径配置
  apiUrls: {
    // 天翼云
    zos: {
      getUploadLink: "/openapi/zos/upload-link", // 获取上传链接
      setObjHeaders: "/openapi/zos/set-object-headers", // 设置对象的 HTTP 头
      setObjAcl: "/openapi/zos/set-object-acl", // 获设置对象ACL
    },
    // 字典
    dictionary: {
      list: "/openapi/dictionary", // 查询字典列表
    },
    // 定位
    location: {
      findNearbyVehicles: "/openapi/location/findNearbyVehicles", // 查找附近车辆
      calculateDistance: "/openapi/location/calculateDistance", // 计算两个经纬度之间的距离
    },
    // 区域模拟
    area: {
      list: "/openapi/areas", // 查询所有行政区
    },
    // 用户模块
    user: {
      getPhoneNumber: "/openapi/weapp/getPhoneNumber", // 获取手机号
      register: "/openapi/user/register", // 注册
      login: "/openapi/weapp/code2Session", // 登录
      updateProfile: "/openapi/user/update", // 更新用户信息，未验证
      uploadAvatar: "/openapi/user/profile", // 上传用户头像，未验证
      getPets: "/customers/{id}/pets", // 查询用户已有宠物
      addPet: "/customers/{id}/pet", // 新增宠物
      editPet: "/customers/{id}/pet/{petId}", // 编辑宠物
      delPet: "/customers/{id}/pet/{petId}", // 删除宠物
      getLastServiceTime: "/customers/{id}/pets/{petId}/last-service-time", // 查询指定宠物最后一次洗护完成时间
    },
    // 地址模块
    address: {
      list: "/customers/{id}/addresses", // 获取客户的地址列表
      add: "/customers/{id}/address", // 增加地址
      update: "/customers/{id}/address/{addressId}", // 编辑地址
      delete: "/customers/{id}/address/{addressId}", // 删除地址
      default: "/customers/{id}/addresses/default", // 获取客户的默认地址
      active: "/customers/{id}/address/{addressId}/default", // 设为默认
    },
    // 服务模块
    service: {
      list: "/openapi/service/types", // 获取服务类目列表
      services: "/openapi/service/services/{typeId}", // 获取指定类目下的服务列表
      additionalService: "/openapi/service/additional-service/{serviceId}", // 获取增项服务
    },
    // 订单模块
    order: {
      list: "/customers/{id}/orders", // 获取订单列表
      detail: "/customers/{id}/order/{orderId}", // 获取订单详情
      add: "/customers/{id}/order", // 新增订单
      pay: "/customers/{customerId}/order/{sn}/pay", // 支付订单
      status: "/customers/{customerId}/order/{sn}/status", // 获取订单状态
      refundRequest: "/customers/{customerId}/applyRefund/{sn}", // 申请退款
      cancel: "/customers/{customerId}/order/{orderId}", // 取消订单
      servicePhotos: "/orders/{orderId}/service-photos", // 查询订单服务照片
      updateServiceAddress: "/orders/{orderId}/updateServiceAddress", // 修改订单服务地址
      updateServiceTime: "/orders/{orderId}/updateServiceTime", // 修改订单服务时间
    },
    // 评价模块
    review: {
      create: "/customers/{customerId}/review", // 创建评价
      getByOrderId: "/reviews/order/{orderId}", // 根据订单ID获取评价
      customerList: "/reviews/customer/{customerId}", // 获取客户评价列表
      serviceList: "/reviews/service/{serviceId}", // 获取服务评价列表
      update: "/reviews/{reviewId}", // 更新评价
      delete: "/reviews/{reviewId}", // 删除评价
    },
    // 照片墙模块
    photoWall: {
      latest: "/photo-walls/latest/list", // 获取最新照片列表
      popular: "/photo-walls/popular/list", // 获取热门照片列表
      detail: "/photo-walls/{id}", // 查看照片详情
      like: "/photo-walls/{id}/like", // 点赞照片
      unlike: "/photo-walls/{id}/unlike", // 取消点赞
      orderPhotos: "/photo-walls/order/{orderId}", // 查询订单相关照片
    },
    // 活动模块
    activity: {
      current: "/openapi/activities/current", // 获取当前发布的活动
      detail: "/openapi/activities/{id}", // 获取活动详情
    },
    // 轮播图模块
    banner: {
      list: "/openapi/banners", // 获取轮播图列表
    },
    // 投诉建议模块
    complaint: {
      create: "/customers/{customerId}/complaint", // 创建投诉建议
      list: "/customers/{customerId}/complaints", // 获取客户投诉建议列表
      detail: "/complaints/{complaintId}", // 获取投诉建议详情
      update: "/complaints/{complaintId}", // 更新投诉建议
      delete: "/complaints/{complaintId}", // 删除投诉建议
      orderComplaints: "/complaints/orders/{orderId}", // 根据订单获取投诉建议
    },
    // 追加服务模块
    additionalService: {
      create: "/order-details/{orderDetailId}/additional-services", // 创建追加服务申请
      list: "/order-details/{orderDetailId}/additional-services", // 查询追加服务列表
      detail: "/order-details/{orderDetailId}/additional-services/{id}", // 查询追加服务详情
      pay: "/order-details/{orderDetailId}/additional-services/{id}/pay", // 支付追加服务订单
      delete: "/order-details/{orderDetailId}/additional-services/{id}", // 删除追加服务申请
      syncPaymentStatus: "/order-details/{orderDetailId}/additional-services/{id}/sync-payment-status", // 同步追加服务支付状态
    },
    pay: {
      jsapi: "/wepay/jsapi", // 下单
      pay_sign: "/wepay/pay_sign", // 获取支付签名
      getTransactionsBySN: "/wepay/transactions/sn/:sn", // 商户订单号查询订单
      refund: "/wepay/refund/:sn", // 退款
    },
    // 权益卡
    rightsCard: {
      list: "/openapi/rights-card", // 查询权限卡类列表
      buy: "/membership-card-orders", // 购买权益卡
      pay: "/membership-card-orders/:sn/pay", // 完成支付
      myCards: "/customers/{customerId}/membership-cards", // 查询我的权益卡
      myValidCards: "/customers/{customerId}/valid-membership-cards", // 获取我的有效权益卡列表
      availableCards: "/customers/{customerId}/available-membership-cards" // 获取可用的权益卡
    },
    // 优惠券
    coupon: {
      list: "/openapi/coupon", // 查询优惠券列表
      buy: "/coupon-orders", // 购买优惠券
      pay: "/coupon-orders/:sn/pay", // 完成支付
      myCoupons: "/customers/{customerId}/coupons", // 查询我的优惠券
      myValidCoupons: "/customers/{customerId}/valid-coupons", // 获取我的有效优惠券表
      availableCoupons: "/customers/{customerId}/available-coupons" // 获取可用的优惠券
    },
    // 推广记录
    promotionRecord: {
      create: '/promotion-record/', // 创建推广记录
    },
    // 消息
    weMessage: {
      subscription: '/subscription', // 订阅
    },
    // 员工推广
    employeePromotion: {
      checkCode: '/employee-promotion/check-code/{promotionCode}', // 检查推广码是否可用
      create: '/employee-promotion/create', // 填写推广码建立推广关系
      getEmployee: '/employee-promotion/customer/{customerId}/employee', // 查看关联的推广员工
    },
    // 员工管理
    employee: {
      list: '/customers/{customerId}/employees', // 获取可选择的员工列表
    },
    // 消息模块
    message: {
      list: '/api/message/list/{userId}',           // GET - 获取消息列表
      detail: '/api/message/detail/{messageId}',    // GET - 获取消息详情
      markAsRead: '/api/message/mark-read/{messageId}',     // POST - 标记已读
      markAllAsRead: '/api/message/mark-all-read/{userId}', // POST - 全部标记已读
      delete: '/api/message/delete/{messageId}',    // DELETE - 删除消息
      unreadCount: '/api/message/unread-count/{userId}',    // GET - 未读数量
    },
  },
};
