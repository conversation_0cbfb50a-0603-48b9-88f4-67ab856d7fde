<view class="container containermessage">
  <diy-navbar isFixed="{{true}}" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-center diygw-col-24">
        消息
      </view>
    </view>
  </diy-navbar>
  <view class="contentmessage">
    <view wx:if="{{newsList.length}}" class="flex flex-wrap diygw-col-24 flex-direction-column listwrapper">
      <view wx:for="{{newsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"
            class="flex diygw-col-24 items-stretch flex-nowrap flex3-clz {{item.isRead ? 'message-read' : 'message-unread'}}"
            data-index="{{index}}" bindtap="onMessageTap">
        <image src="{{item.icon}}" class="image3-size diygw-image diygw-col-0 image3-clz" mode="widthFix"></image>
        <view class="flex flex-wrap diygw-col-24 flex-direction-column justify-between items-start flex5-clz">
          <view class="flex diygw-col-24 justify-between items-center flex-nowrap">
            <text class="diygw-col-0 text8-clz diygw-ellipsis">{{item.title}} </text>
            <text class="diygw-col-0"> {{item.time}} </text>
          </view>
          <view class="flex diygw-col-24 justify-between items-center flex-nowrap">
            <text class="diygw-col-0 text10-clz diygw-ellipsis"> {{item.content}} </text>
            <text wx:if="{{!item.isRead && item.count > 0}}" class="diygw-col-0 text11-clz"> {{item.count>99 ? '99+': item.count}} </text>
          </view>
        </view>
        <view wx:if="{{!item.isRead}}" class="unread-indicator"></view>
      </view>
    </view>
    <view wx:else class="flex flex-wrap diygw-col-24 items-center justify-center direction-column">
      <empty icon="//xian7.zos.ctyun.cn/pet/static/nomessage.png" text="暂无信息" class="empty" />
    </view>
  </view>
  <custom-tabbar currentActive='message' />
</view>