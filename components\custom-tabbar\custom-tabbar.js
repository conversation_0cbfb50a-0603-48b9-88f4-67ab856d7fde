// components/custom-tabbar.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    currentActive: {
      type: String,
      value: ''
    },
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    redirect(evt) {
      let {
        type
      } = evt.currentTarget.dataset;

      let url = '/pages/' + type + '/index';

      // 如果是点击服务按钮，添加fromNav参数以清除已选员工信息
      if (type === 'service') {
        url += '?fromNav=true';
      }

      wx.redirectTo({
        url: url,
      })
    }
  }
})