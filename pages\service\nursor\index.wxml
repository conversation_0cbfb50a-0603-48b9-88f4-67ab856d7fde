<!--pages/service/nursor/index.wxml-->
<view class="nursor-page">
  <view class="nursor-bg">
    <image src='https://xian7.zos.ctyun.cn/pet/static/hlbg.png' mode="widthFix"></image>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && list.length === 0}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 员工列表 -->
  <view wx:else class="nursor-list">
    <view class="nursor-list-item" wx:for="{{list}}" wx:key="id">
      <view class="employee-card">
        <!-- 头像区域 -->
        <view class="employee-avatar-section">
          <image src="{{item.avatar || 'https://xian7.zos.ctyun.cn/pet/static/avatar.png'}}" class="employee-avatar" mode="aspectFill"></image>
        </view>

        <!-- 信息区域 -->
        <view class="employee-info-section">
          <!-- 姓名 -->
          <view class="employee-name-row">
            <text class="employee-name">{{item.name}}</text>
          </view>

          <!-- 标签行 -->
          <view class="employee-tags-row">
            <text wx:if="{{item.positionName}}" class="tag position-tag">{{item.positionName}}</text>
            <text class="tag level-tag">等级{{item.level || 1}}</text>
            <text wx:if="{{item.formattedWorkExp}}" class="tag exp-tag">{{item.formattedWorkExp}}</text>
          </view>

          <!-- 评分行 -->
          <view class="employee-rating-row">
            <view class="rating-stars">
              <text wx:for="{{[1,2,3,4,5]}}" wx:for-item="rateitem" wx:for-index="index" class="star" style="color:{{rateitem<=(item.rating || 0)?'#F54E06':'#eee'}}">★</text>
            </view>
            <text class="rating-score">{{item.rating || 0}}</text>
          </view>

          <!-- 车辆信息行 -->
          <view wx:if="{{item.vehicle && item.vehicle.plateNumber}}" class="employee-vehicle-row">
            <text class="vehicle-info">{{item.vehicle.plateNumber}}{{item.vehicle.vehicleTypeName ? ' · ' + item.vehicle.vehicleTypeName : ''}}</text>
            <text class="vehicle-status {{item.vehicle.status === '空闲' ? 'status-free' : 'status-busy'}}">{{item.vehicle.status}}</text>
          </view>
        </view>

        <!-- 选择按钮区域 -->
        <view class="employee-action-section">
          <view class="select-btn" data-employee="{{item}}" bind:tap="redirect">
            <text class="select-text">选择</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading && list.length > 0}}" class="load-more">
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && list.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && list.length === 0}}" class="empty-state">
      <image src="https://xian7.zos.ctyun.cn/pet/static/nopj.png" class="empty-icon"></image>
      <text class="empty-text">暂无可选择的服务人员</text>
    </view>
  </view>
</view>