import locationApi from '../../api/modules/location';

Page({
	data: {
		longitude: null,
		latitude: null,
		markers: [],
		showRetry: false,
		lastSelectedVehicleId: null,
		selectedIndex: 0,
	},

	onShow() {
		this.startLocationUpdate();
	},

	startLocationUpdate() {
		this.updateLocation();
		// 设置定时器，每30秒刷新一次车辆位置
		this.vehicleTimer = setInterval(() => {
			this.findNearbyVehicles();
		}, 30000);
	},

	onHide() {
		if (this.vehicleTimer) {
			clearInterval(this.vehicleTimer);
			this.vehicleTimer = null;
		}
	},

	onUnload() {
		if (this.vehicleTimer) {
			clearInterval(this.vehicleTimer);
			this.vehicleTimer = null;
		}
		if (this.data.idleTimer) {
			clearTimeout(this.data.idleTimer);
			this.setData({ idleTimer: null });
		}
	},

	updateLocation() {
		wx.getLocation({
			altitude: true,
			highAccuracyExpireTime: 0,
			isHighAccuracy: true,
			type: 'gcj02',
			success: (res) => {
				const { latitude, longitude } = res;
				this.setData(
					{
						latitude,
						longitude,
						showRetry: false,
					},
					() => {
						this.findNearbyVehicles();
					}
				);
			},
			fail: (err) => {
				console.error('获取位置失败:', err);
				this.stopPolling();
				this.setData({ showRetry: true });
				wx.showToast({
					title: '获取位置失败',
					icon: 'none',
				});
			},
		});
	},

	stopPolling() {
		if (this.vehicleTimer) {
			clearInterval(this.vehicleTimer);
			this.vehicleTimer = null;
		}
	},

	findNearbyVehicles() {
		const { latitude, longitude, lastSelectedVehicleId } = this.data;
		if (!latitude || !longitude) return;

		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => reject(new Error('请求超时')), 10000);
		});

    Promise.race([locationApi.findNearbyVehicles({ lat: latitude, lng: longitude }), timeoutPromise])
      .then(list => {
        const vehiclesWithDistance = list.sort((a, b) => a.distance - b.distance);

				const validVehicles = vehiclesWithDistance.map((vehicle, index) => ({
					...vehicle,
					distanceText:
						vehicle.distance >= 1000
							? `${(vehicle.distance / 1000).toFixed(1)}公里`
							: `${Math.round(vehicle.distance)}米`,
					isNearest: index === 0,
					// 确保状态字段存在，如果没有则设为空闲
					status: vehicle.status || '空闲',
				}));

				const markers = validVehicles.map((vehicle, index) => ({
					id: index,
					latitude: Number(vehicle.latitude),
					longitude: Number(vehicle.longitude),
					iconPath: 'https://xian7.zos.ctyun.cn/pet/static/car.png',
					width: 30,
					height: 30,
					callout: {
            content: `车牌: ${vehicle.plateNumber || '未知'}\n距离: ${vehicle.distanceText}`,
						color: '#333',
						fontSize: 12,
						borderRadius: 4,
						bgColor: '#fff',
						padding: 5,
						display: 'ALWAYS',
					},
				}));

				this.setData(
					{
						vehicles: validVehicles,
						markers,
					},
					() => {
						// 只有当没有选择过车辆时才自动缩放
						if (!lastSelectedVehicleId) {
							this.focusNearestVehicle();
						}
					}
				);
			})
      .catch(err => {
				console.error('获取车辆失败:', err);
				this.stopPolling();
				this.setData({ showRetry: true });
				wx.showToast({
					title: err.message.includes('超时') ? '请求超时' : '获取车辆信息失败',
					icon: 'none',
				});
			});
	},

	focusNearestVehicle() {
    if (!this.data.vehicles?.length || !this.data.latitude || !this.data.longitude) return;

		const nearestVehicle = this.data.vehicles[0];
		this.mapCtx = this.mapCtx || wx.createMapContext('map');

		this.mapCtx.includePoints({
			points: [
				{ latitude: this.data.latitude, longitude: this.data.longitude },
				{
					latitude: nearestVehicle.latitude,
					longitude: nearestVehicle.longitude,
				},
			],
			padding: [80, 80, 80, 80],
		});
	},

	includeAllMarkers() {
    if (!this.data.latitude || !this.data.longitude || this.data.markers.length === 0) return;

		this.mapCtx = this.mapCtx || wx.createMapContext('map');

		const points = [
			{ latitude: this.data.latitude, longitude: this.data.longitude },
      ...this.data.markers.map(marker => ({
				latitude: marker.latitude,
				longitude: marker.longitude,
			})),
		];

		this.mapCtx.includePoints({
			points: points,
			padding: [80, 80, 80, 80],
		});
	},

	moveToLocation() {
		if (this.data.latitude && this.data.longitude) {
			this.setData({
				selectedIndex: 0,
				lastSelectedVehicleId: null,
			});
			this.mapCtx = this.mapCtx || wx.createMapContext('map');
			this.mapCtx.moveToLocation();
			this.findNearbyVehicles();
		}
	},

	onVehicleTap(e) {
		const index = e.currentTarget.dataset.index;
		const vehicle = this.data.vehicles[index];

		if (!vehicle || !this.data.latitude || !this.data.longitude) return;

		this.setData({
			selectedIndex: index,
			lastSelectedVehicleId: vehicle.id,
		});

		this.mapCtx = this.mapCtx || wx.createMapContext('map');
		this.mapCtx.includePoints({
			points: [
				{ latitude: this.data.latitude, longitude: this.data.longitude },
				{ latitude: vehicle.latitude, longitude: vehicle.longitude },
			],
			padding: [80, 80, 80, 80],
		});
	},

	// 地图标记点击事件
	markertap(e) {
		const markerId = e.detail.markerId;
		if (markerId >= 0 && markerId < this.data.vehicles.length) {
			this.setData({
				selectedIndex: markerId,
				lastSelectedVehicleId: this.data.vehicles[markerId].id,
			});
		}
	},

	// 地图区域变化事件
	regionchange(e) {
		// 可以在这里处理地图区域变化的逻辑
		console.log('地图区域变化:', e.detail);
	},
});
