import request, { analysisRes } from "../request";
import config from "../config";

const { photoWall } = config.apiUrls;

export default {
  // 获取最新照片列表
  async getLatestList(params = {}) {
    const res = await request.get(photoWall.latest, params);
    return analysisRes(res);
  },

  // 获取热门照片列表
  async getPopularList(params = {}) {
    const res = await request.get(photoWall.popular, params);
    return analysisRes(res);
  },

  // 查看照片详情
  async getDetail(id) {
    const res = await request.get(photoWall.detail.replace("{id}", id));
    return analysisRes(res);
  },

  // 点赞照片
  async like(id) {
    const res = await request.post(photoWall.like.replace("{id}", id));
    return analysisRes(res);
  },

  // 取消点赞
  async unlike(id) {
    const res = await request.post(photoWall.unlike.replace("{id}", id));
    return analysisRes(res);
  },

  // 查询订单相关照片
  async getOrderPhotos(orderId) {
    const res = await request.get(photoWall.orderPhotos.replace("{orderId}", orderId));
    return analysisRes(res);
  },
};
