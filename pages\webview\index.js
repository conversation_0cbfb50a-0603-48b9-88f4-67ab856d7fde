Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    webviewUrl: '', // WebView要加载的URL
    showError: false, // 是否显示错误页面
    errorMessage: '页面加载失败', // 错误信息
  },

  onLoad(options) {
    const { url } = options;
    
    if (!url) {
      this.setData({
        showError: true,
        errorMessage: '缺少页面地址参数',
      });
      return;
    }

    try {
      // 解码URL
      const decodedUrl = decodeURIComponent(url);
      
      // 简单的URL验证
      if (!this.isValidUrl(decodedUrl)) {
        this.setData({
          showError: true,
          errorMessage: '无效的页面地址',
        });
        return;
      }

      this.setData({
        webviewUrl: decodedUrl,
      });
    } catch (error) {
      console.error('URL解码失败:', error);
      this.setData({
        showError: true,
        errorMessage: 'URL格式错误',
      });
    }
  },

  /**
   * 简单的URL验证
   */
  isValidUrl(url) {
    try {
      // 检查是否以http或https开头
      return /^https?:\/\/.+/.test(url);
    } catch (error) {
      return false;
    }
  },

  /**
   * WebView加载错误处理
   */
  onWebViewError(event) {
    console.error('WebView加载错误:', event.detail);
    this.setData({
      showError: true,
      errorMessage: '页面加载失败，请检查网络连接',
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到首页
        wx.redirectTo({
          url: '/pages/index/index'
        });
      }
    });
  },

  /**
   * 重新加载
   */
  reload() {
    if (this.data.webviewUrl) {
      this.setData({
        showError: false,
      });
      // 重新设置URL来触发WebView重新加载
      const url = this.data.webviewUrl;
      this.setData({
        webviewUrl: '',
      });
      setTimeout(() => {
        this.setData({
          webviewUrl: url,
        });
      }, 100);
    }
  },
});
