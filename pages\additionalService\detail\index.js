import additionalServiceApi from '../../../api/modules/additionalService.js';
import payApi from '../../../api/modules/pay.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    additionalServiceId: null,

    // 追加服务详情
    serviceDetail: null,
    loading: true,
    paying: false,

    // 状态映射
    statusMap: {
      pending_confirm: {
        text: '待确认',
        color: '#ff9500',
        desc: '您的追加服务申请已提交，等待员工确认',
        icon: '⏳',
      },
      confirmed: {
        text: '已确认',
        color: '#007aff',
        desc: '员工已确认您的申请，请尽快完成支付',
        icon: '✅',
      },
      rejected: {
        text: '已拒绝',
        color: '#ff3b30',
        desc: '很抱歉，您的申请被拒绝',
        icon: '❌',
      },
      pending_payment: {
        text: '待付款',
        color: '#ff9500',
        desc: '请尽快完成支付',
        icon: '💳',
      },
      paid: {
        text: '已付款',
        color: '#34c759',
        desc: '支付成功，服务进行中',
        icon: '💰',
      },
      completed: {
        text: '已完成',
        color: '#34c759',
        desc: '追加服务已完成',
        icon: '✅',
      },
      cancelled: {
        text: '已取消',
        color: '#8e8e93',
        desc: '服务已取消',
        icon: '❌',
      },
      refunding: {
        text: '退款中',
        color: '#ff9500',
        desc: '退款处理中，请耐心等待',
        icon: '⏳',
      },
      refunded: {
        text: '已退款',
        color: '#8e8e93',
        desc: '退款已完成',
        icon: '💰',
      },
    },

    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: [],
  },

  onLoad(options) {
    const { orderDetailId, id } = options;
    const userInfo = Session.getUser();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
      additionalServiceId: parseInt(id),
    });

    this.loadServiceDetail();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadServiceDetail();
  },

  /**
   * 加载追加服务详情
   */
  async loadServiceDetail() {
    try {
      this.setData({ loading: true });

      const { orderDetailId, additionalServiceId } = this.data;
      const detail = await additionalServiceApi.detail(orderDetailId, additionalServiceId);

      if (detail) {
        // 格式化数据
        const statusInfo = this.data.statusMap[detail.status] || {
          text: detail.status,
          color: '#999',
          desc: '',
          icon: '❓',
        };

        // 如果是拒绝状态，优先使用rejectReason字段作为描述
        if (detail.status === 'rejected' && detail.rejectReason) {
          statusInfo.desc = detail.rejectReason;
        }

        const formattedDetail = {
          ...detail,
          createdAt: detail.createdAt ? utils.formatNormalDate(detail.createdAt) : '',
          confirmTime: detail.confirmTime ? utils.formatNormalDate(detail.confirmTime) : '',
          statusInfo: statusInfo,
        };

        this.setData({ serviceDetail: formattedDetail });
      } else {
        // detail为null说明API返回了业务错误（如400），analysisRes已经显示了错误toast
        // 没有加载到任何信息时，延迟后返回到订单详情页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      // 只有真正的网络错误或其他异常才返回上一页
      console.error('加载追加服务详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
      });
      // 网络错误时才返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 去支付 - 直接弹出支付确认框
   */
  goPay() {
    const { serviceDetail } = this.data;

    if (!serviceDetail) return;

    // 检查状态 - 已确认和待付款状态都支持支付
    if (serviceDetail.status !== 'confirmed' && serviceDetail.status !== 'pending_payment') {
      wx.showToast({
        title: '当前状态不支持支付',
        icon: 'none',
      });
      return;
    }

    // 显示支付确认模态框
    this.setData({
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${serviceDetail.totalFee} 吗？`,
      modalButtons: [
        { text: '取消', type: 'cancel', event: 'handleModalCancel' },
        { text: '确认支付', type: 'primary', event: 'handlePayConfirm' },
      ],
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail() {
    const { serviceDetail } = this.data;

    if (serviceDetail && serviceDetail.orderDetail) {
      const orderData = JSON.stringify(serviceDetail.orderDetail.order);
      wx.navigateTo({
        url: `/pages/serviceOrder/orderDetail/index?data=${encodeURIComponent(orderData)}`,
      });
    }
  },

  /**
   * 联系客服
   */
  contactService() {
    this.setData({
      showModal: true,
      modalTitle: '联系客服',
      modalContent: '如有疑问，请联系客服：400-123-4567',
      modalButtons: [
        {
          text: '确定',
          type: 'primary',
          event: 'handleModalConfirm',
        },
      ],
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadServiceDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 处理支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });

    // 先同步支付状态，检查是否已经支付
    wx.showLoading({ title: '检查支付状态...' });

    const syncResult = await this.syncPaymentStatus();
    wx.hideLoading();

    if (syncResult) {
      // 同步成功，重新加载数据检查状态
      await this.loadServiceDetail();
      const { serviceDetail } = this.data;

      if (serviceDetail && serviceDetail.status === 'paid') {
        // 已经是已支付状态，直接显示成功
        this.showPaymentSuccessModal();
        return;
      }
    }

    // 如果未支付或同步失败，继续正常支付流程
    await this.processPay();
  },

  /**
   * 处理支付
   */
  async processPay() {
    const { userInfo, orderDetailId, additionalServiceId, serviceDetail } = this.data;

    if (!serviceDetail || this.data.paying) return;

    try {
      this.setData({ paying: true });

      // 处理0元订单
      if (Number(serviceDetail.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });
        const result = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);
        wx.hideLoading();

        if (result) {
          // 0元订单支付成功后刷新数据并显示成功
          await this.loadServiceDetail();
          this.showPaymentSuccessModal();
        } else {
          wx.showToast({ title: '支付失败，请重试', icon: 'none' });
        }
        return;
      }

      // 正常支付流程
      wx.showLoading({ title: '发起支付...' });
      const payResult = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);
      wx.hideLoading();

      if (payResult && payResult.orderSn) {
        // 调用微信支付
        const _this = this;
        payApi.doPay({
          sn: payResult.orderSn,
          onOk: async () => {
            // 支付成功后刷新数据并显示成功
            await _this.loadServiceDetail();
            _this.showPaymentSuccessModal();
          },
          onCancel: () => wx.showToast({ title: '取消支付', icon: 'none' }),
          onError: () => wx.showToast({ title: '支付失败', icon: 'none' }),
        });
      } else {
        wx.showToast({ title: '支付失败，请重试', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({ title: '支付失败，请重试', icon: 'none' });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示支付成功模态框
   */
  showPaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [{ text: '确定', type: 'primary', event: 'handlePaymentSuccess' }],
    });
  },

  /**
   * 处理支付成功
   */
  handlePaymentSuccess() {
    this.setData({ showModal: false });
    // 返回上一页并刷新
    wx.navigateBack();
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },

  /**
   * 同步支付状态
   */
  async syncPaymentStatus() {
    const { orderDetailId, additionalServiceId, userInfo } = this.data;

    try {
      console.log('开始同步追加服务支付状态...');
      const syncResult = await additionalServiceApi.syncPaymentStatus(orderDetailId, additionalServiceId, userInfo.id);

      if (syncResult && syncResult.success) {
        console.log('支付状态同步成功:', syncResult.message);
        return true;
      } else {
        console.log('支付状态同步失败:', syncResult?.message || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('同步支付状态异常:', error);
      return false;
    }
  },
});
