<!-- pages/orderReview/orderReview.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">评价</text>
  </view>

  <!-- 订单信息 -->
  <view class="order-info" wx:if="{{orderDetail && orderDetail.orderDetails && orderDetail.orderDetails.length > 0}}">
    <view class="order-content">
      <image class="service-image" src="{{orderDetail.orderDetails[0].service.logo}}" mode="aspectFill"></image>
      <view class="service-info">
        <text class="service-name">{{orderDetail.orderDetails[0].service.serviceName}}</text>
        <text class="service-price">¥{{orderDetail.totalFee}}</text>
      </view>
    </view>
  </view>

  <!-- 评分区域 -->
  <view class="rating-section">
    <view class="section-title">
      <text>服务评分</text>
    </view>
    <view class="rating-container">
      <view class="stars">
        <view wx:for="{{[1,2,3,4,5]}}" wx:key="index" class="star {{rating >= item ? 'active' : ''}}" bindtap="setRating" data-rating="{{item}}">
          <text class="star-icon">★</text>
        </view>
      </view>
      <text class="rating-text">{{ratingTexts[rating-1] || '请评分'}}</text>
      <text class="rating-score">{{rating}}.0</text>
    </view>
  </view>

  <!-- 评价内容 -->
  <view class="comment-section">
    <view class="section-title">
      <text>服务感受如何？</text>
      <text class="placeholder-text">来分享您的体验吧</text>
    </view>
    <textarea 
      class="comment-input" 
      placeholder="{{placeholder}}"
      value="{{comment}}"
      bindinput="onCommentInput"
      maxlength="500"
      auto-height
    ></textarea>
    <view class="char-count">{{comment.length}}/500</view>
  </view>

  <!-- 图片上传 -->
  <view class="photo-section">
    <view class="section-title">
      <text>上传照片</text>
      <text class="optional-text">（可选）</text>
    </view>
    <view class="photo-container">
      <!-- 已上传的图片 -->
      <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
        <image src="{{item}}" class="photo-preview" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
        <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
          <text class="delete-icon">×</text>
        </view>
      </view>
      
      <!-- 上传按钮 -->
      <view wx:if="{{photoList.length < 6}}" class="photo-upload" bindtap="chooseImage">
        <view class="upload-icon">
          <text class="upload-text">📷</text>
        </view>
        <text class="upload-label">上传照片</text>
      </view>
    </view>
    <view class="photo-tip">最多可上传6张照片</view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="submitReview" disabled="{{!canSubmit}}">
      提交
    </button>
  </view>
</view>

<!-- 加载提示 -->
<view class="loading-mask" wx:if="{{isSubmitting}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">提交中...</text>
  </view>
</view>
