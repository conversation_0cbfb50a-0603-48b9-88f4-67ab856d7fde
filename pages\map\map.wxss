/* pages/map/map.wxss */
.vehicle-list {
	padding: 20rpx;
	background-color: red;
}

.list-title {
	font-size: 32rpx;
	font-weight: bold;
  margin-bottom: 20rpx;
  margin-top: 10rpx;
}

.vehicle-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.vehicle-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.plate-number {
	font-size: 28rpx;
}

.vehicle-status {
	font-size: 24rpx;
	padding: 0 10rpx;
	border-radius: 4rpx;
}

.available {
	color: #1989fa;
	background-color: #ecf5ff;
	height: auto;
	line-height: 1.4;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.busy {
	color: #f76260;
	background-color: #fef0f0;
	height: auto;
	line-height: 1.4;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.distance-info {
	font-size: 24rpx;
	color: #999;
}

.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.map-container {
	height: 60vh;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.map-actions {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	z-index: 999;
}

.vehicle-container {
	height: 40vh;
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	padding: 20rpx;
	box-sizing: border-box;
}

.list-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 10rpx;
	border-left: 6rpx solid #1989fa;
}

.available {
	color: #1989fa;
	background-color: #ecf5ff;
	height: fit-content;
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

.vehicle-item {
	padding: 24rpx;
	margin: 0 10rpx 10rpx 10rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05), 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
	border: 1px solid #e5e5e5;
}

.vehicle-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.plate-number {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	letter-spacing: 1rpx;
}

.vehicle-status {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 10rpx;
	font-weight: 500;
}

.available {
	color: #07c160;
	background-color: rgba(7, 193, 96, 0.1);
}

.busy {
	color: #f76260;
	background-color: rgba(247, 98, 96, 0.1);
}

.distance-info {
	font-size: 24rpx;
	color: #666;
	letter-spacing: 1rpx;
}

.map-actions {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.icon-img {
	width: 50rpx;
	height: 50rpx;
}

.nearest-tag {
  background-color: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
}

.list-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.refresh-icon {
  width: 36rpx;
  height: 36rpx;
}

.vehicle-item.selected {
    border: 1px solid #1989fa;
    background-color: #f5faff;
    box-shadow: 0 2rpx 12rpx rgba(25, 137, 250, 0.2);
}

/* 重试容器样式 */
.retry-container {
  height: 40vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-content {
  text-align: center;
}

.retry-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.retry-button {
  background-color: #1989fa;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 无车辆提示样式 */
.no-vehicle-container {
  height: 40vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-vehicle-content {
  text-align: center;
}

.no-vehicle-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.refresh-button {
  background-color: #1989fa;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}