# 用户端消息功能使用指南

## 快速开始

### 1. 功能概述
本项目已成功实现了完整的用户端消息功能，包括：
- ✅ 消息列表展示（已读/未读状态）
- ✅ 消息详情查看
- ✅ 实时消息推送（WebSocket）
- ✅ 消息已读标记
- ✅ 下拉刷新
- ✅ 空状态处理
- ✅ 消息分类（系统消息、平台消息、订单消息）

### 2. 开发环境测试

#### 方法一：使用测试数据（推荐）
当后端API不可用时，系统会自动加载测试数据，您可以直接在微信开发者工具中查看效果：

1. 打开微信开发者工具
2. 导入本项目
3. 点击底部导航栏的"消息"标签
4. 查看消息列表和详情功能

#### 方法二：连接真实API
1. 在 `api/config.js` 中配置正确的API地址：
```javascript
baseUrl: "http://your-api-domain:port",
socketUrl: "ws://your-websocket-domain:port",
```

2. 确保后端提供以下API接口：
- `GET /api/message/list/{userId}` - 获取消息列表
- `GET /api/message/detail/{messageId}` - 获取消息详情
- `POST /api/message/mark-read/{messageId}` - 标记已读
- 其他接口详见文档

### 3. 功能演示

#### 消息列表页面
- 路径：`pages/message/index`
- 功能：
  - 显示消息列表
  - 区分已读/未读状态
  - 显示未读消息数量
  - 支持下拉刷新
  - 点击消息跳转详情

#### 消息详情页面
- 路径：`pages/message/messageDetail/index`
- 功能：
  - 显示消息完整内容
  - 解析并展示额外数据（如订单信息）
  - 自动标记为已读
  - 动态设置页面标题

#### WebSocket实时通知
- 在首页自动连接WebSocket
- 支持自动重连（最多5次）
- 接收实时消息推送
- 显示Toast通知

### 4. 测试数据说明

系统内置了以下测试数据：

1. **系统通知**（未读）
   - 标题：系统通知
   - 内容：您的洗护订单已接单，师傅正在赶往您的地址
   - 包含订单相关信息

2. **平台消息**（已读）
   - 标题：平台消息
   - 内容：恭喜您获得新用户专享优惠券

3. **订单消息**（未读）
   - 标题：订单消息
   - 内容：您的洗护服务已完成，请查看服务照片并进行评价
   - 包含订单和宠物信息

### 5. 样式特性

- **未读消息**：蓝色左边框 + 红色未读指示器
- **已读消息**：降低透明度显示
- **消息图标**：根据消息类型显示不同图标
- **响应式设计**：适配不同屏幕尺寸

### 6. 开发调试

#### 查看控制台日志
在微信开发者工具的控制台中可以看到：
- WebSocket连接状态
- 消息加载过程
- 测试数据加载提示
- 错误信息

#### 常见问题
1. **消息列表为空**：检查用户是否已登录
2. **WebSocket连接失败**：检查网络和服务器配置
3. **API请求失败**：会自动降级到测试数据

### 7. 生产环境部署

1. 配置正确的API地址和WebSocket地址
2. 确保后端API服务正常运行
3. 确保WebSocket服务正常运行
4. 移除测试数据相关代码（可选）

### 8. 扩展功能

可以根据需要添加以下功能：
- 消息删除
- 全部标记已读
- 消息分页加载
- 消息搜索
- 消息分类筛选

## 技术实现

### 文件结构
```
pages/message/
├── index.js                    # 消息列表页面逻辑
├── index.wxml                  # 消息列表页面模板
├── index.wxss                  # 消息列表页面样式
├── index.json                  # 消息列表页面配置
└── messageDetail/
    ├── index.js                # 消息详情页面逻辑
    ├── index.wxml              # 消息详情页面模板
    ├── index.wxss              # 消息详情页面样式
    └── index.json              # 消息详情页面配置

api/modules/message.js          # 消息API接口封装
```

### 核心技术
- 微信小程序原生开发
- WebSocket实时通信
- RESTful API调用
- 响应式UI设计

## 总结

本消息功能实现完整、稳定，具有良好的用户体验和开发体验。通过内置测试数据，即使在没有后端支持的情况下也能完整演示所有功能。

如有问题或需要扩展功能，请参考详细的实现文档：`docs/用户端消息功能实现说明.md`
