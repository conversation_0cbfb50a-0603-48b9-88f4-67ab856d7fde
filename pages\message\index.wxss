/* pages/message/index.wxss */
.containermessage{
  background-color: #f5f5f5;
  padding: 24rpx;
  padding-bottom: 160rpx;
  min-height: 100vh;
  background: url('https://xian7.zos.ctyun.cn/pet/static/bj.png') no-repeat;
  background-size: contain;
  box-sizing: border-box;
}
.contentmessage{
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  min-height: 400rpx;
  overflow-y: auto;
  position: relative;
}
.listwrapper{
  padding: 24rpx;
  min-height: 200rpx;
}
.flex3-clz {
  padding: 16rpx;
  border-bottom: 1px solid #dadada;
}
.image3-clz {
	flex-shrink: 0;
	border-radius: 100rpx;
	overflow: hidden;
	width: 100rpx !important;
}
.image3-size {
	height: 100rpx !important;
	width: 100rpx !important;
}
.flex5-clz {
	overflow: hidden;
	padding:0 0 0 10rpx;
}
.text8-clz {
	color: #5d5d5d;
	font-size: 28rpx !important;
}
.text10-clz {
	color: #5d5d5d;
	flex: 1;
}
.text11-clz {
	background-color: #fa2e2e;
  border-radius: 30rpx;
  width: 48rpx;
  line-height: 48rpx;
  text-align: center;
	overflow: hidden;
	color: #ffffff;
	font-size: 20rpx;
}

/* 消息状态样式 */
.message-unread {
  background-color: #f8f9ff;
  border-left: 4rpx solid #007aff;
}

.message-read {
  opacity: 0.7;
}

.unread-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ff3b30;
  border-radius: 50%;
}

.flex3-clz {
  position: relative;
}