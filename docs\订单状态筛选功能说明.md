# 订单列表状态筛选功能

## 功能概述

为订单列表页面添加了二级状态筛选功能，解决了"进行中"和"已完成"标签下包含多种状态，不便于查看特定状态订单的问题。

## 功能特性

### 1. 主标签保持不变
- 全部
- 待付款
- 待接单
- 进行中（包含多个子状态）
- 已完成（包含多个子状态）

### 2. 二级筛选器
当用户点击"进行中"或"已完成"标签时，会在标签下方展开对应的子状态选项：

#### 进行中子状态：
- 全部（显示所有进行中状态）
- 待服务
- 已出发
- 服务中
- 退款中

#### 已完成子状态：
- 全部（显示所有已完成状态）
- 已完成
- 已退款
- 已评价

### 3. 交互设计
- **展开/收起**：点击有子筛选器的标签可展开/收起子选项
- **Radio选择**：子筛选器采用radio样式，支持单选
- **视觉反馈**：选中状态有明显的视觉反馈（颜色变化、选中圆点）
- **动画效果**：展开/收起有平滑的动画过渡

## 技术实现

### 1. 数据结构扩展
```javascript
orderTabs: [
  {
    name: '进行中',
    status: '待服务,已出发,服务中,退款中',
    hasSubFilters: true,
    subFilters: [
      { name: '全部', status: '待服务,已出发,服务中,退款中' },
      { name: '待服务', status: '待服务' },
      // ...
    ]
  }
]
```

### 2. 状态管理
- `currentTab`: 当前选中的主标签
- `currentSubFilter`: 当前选中的子筛选器
- `showSubFilters`: 是否显示子筛选器

### 3. 筛选逻辑
- 优先使用子筛选器的状态进行API查询
- 如果没有子筛选器，使用主标签状态

## 用户体验优化

1. **快速筛选**：用户可以快速在具体状态间切换
2. **状态明确**：每个状态都有清晰的标识
3. **操作简单**：点击即可切换，无需额外操作
4. **视觉清晰**：选中状态有明显的视觉区分

## 兼容性

- 保持原有功能完全兼容
- 对于没有子筛选器的标签，行为保持不变
- 支持通过URL参数初始化特定标签状态
