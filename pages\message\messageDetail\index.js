// pages/message/messageDetail/index.js
const utils = require('../../utils/util');
import messageApi from '../../../api/modules/message.js';

Page({
  data: {
    messageId: '',
    messageDetail: null,
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ messageId: options.id });
      this.loadMessageDetail();
    }
  },

  // 加载消息详情
  async loadMessageDetail() {
    try {
      const res = await messageApi.getMessageDetail(this.data.messageId);
      if (res) {
        const messageDetail = {
          ...res,
          time: utils.formatNormalDate(res.createdAt),
          icon: this.getMessageIcon(res.type),
          parsedExtraData: this.parseExtraData(res.extraData)
        };
        this.setData({ messageDetail });

        // 动态设置页面标题
        const pageTitle = (res.title && res.title.trim()) ? res.title : '消息详情';
        wx.setNavigationBarTitle({
          title: pageTitle
        });

        // 如果消息未读，自动标记为已读
        if (!res.isRead) {
          this.markAsRead();
        }
      }
    } catch (error) {
      console.error('获取消息详情失败:', error);
      // 在开发阶段，如果API不可用，显示测试数据
      if (error.message && error.message.includes('Network Error')) {
        this.loadTestDetail();
      } else {
        wx.showToast({
          title: '获取消息详情失败',
          icon: 'none'
        });
      }
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载测试详情数据（开发阶段使用）
  loadTestDetail() {
    const testDetail = {
      id: this.data.messageId,
      type: 'system',
      title: '系统通知',
      content: '您的洗护订单已接单，师傅正在赶往您的地址。预计30分钟内到达，请保持手机畅通。如有疑问，请及时联系客服。',
      createdAt: new Date().toISOString(),
      isRead: false,
      extraData: JSON.stringify({
        orderId: 'ORD123456',
        orderNo: 'SN202501060001',
        serviceTime: new Date().toISOString(),
        serviceAddress: '北京市朝阳区某某小区1号楼2单元301室',
        petInfo: '小白（金毛，2岁）',
        amount: '88.00',
        remark: '宠物比较胆小，请师傅温柔一些'
      })
    };

    const messageDetail = {
      ...testDetail,
      time: utils.formatNormalDate(testDetail.createdAt),
      icon: this.getMessageIcon(testDetail.type),
      parsedExtraData: this.parseExtraData(testDetail.extraData)
    };

    this.setData({ messageDetail });

    // 动态设置页面标题
    wx.setNavigationBarTitle({
      title: testDetail.title || '消息详情'
    });

    console.log('已加载测试详情数据，用于开发调试');
  },

  // 根据消息类型获取图标
  getMessageIcon(type) {
    const iconMap = {
      'system': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
      'platform': '//xian7.zos.ctyun.cn/pet/static/msg2.png',
      'order': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
    };
    return iconMap[type] || '//xian7.zos.ctyun.cn/pet/static/msg1.png';
  },

  // 解析额外数据
  parseExtraData(extraData) {
    if (!extraData) return null;

    try {
      let data = extraData;
      if (typeof extraData === 'string') {
        data = JSON.parse(extraData);
      }

      if (typeof data !== 'object' || data === null) {
        return null;
      }

      // 转换为显示格式
      const displayData = [];
      const fieldMap = {
        orderId: '订单ID',
        orderNo: '订单号',
        customerName: '客户姓名',
        serviceTime: '服务时间',
        serviceAddress: '服务地址',
        petInfo: '宠物信息',
        amount: '金额',
        remark: '备注'
      };

      for (const [key, value] of Object.entries(data)) {
        if (value !== null && value !== undefined && value !== '') {
          const label = fieldMap[key] || key;
          let displayValue = value;

          // 特殊处理时间格式
          if (key === 'serviceTime' && value) {
            displayValue = utils.formatNormalDate(value);
          }

          displayData.push({
            label,
            value: displayValue
          });
        }
      }

      return displayData.length > 0 ? displayData : null;
    } catch (error) {
      console.error('解析额外数据失败:', error);
      return null;
    }
  },

  // 标记消息为已读
  async markAsRead() {
    try {
      await messageApi.markAsRead(this.data.messageId);
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
    };
  },

  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
    };
  },
});
