import request, { analysisRes } from '../request';
import config from '../config';

const { message } = config.apiUrls;

export default {
  // 获取消息列表
  async getMessageList(userId, type = '') {
    const params = {};
    if (type) {
      params.type = type;
    }
    const res = await request.get(message.list.replace('{userId}', userId), params);
    const data = analysisRes(res);
    return data;
  },

  // 获取消息详情
  async getMessageDetail(messageId) {
    const res = await request.get(message.detail.replace('{messageId}', messageId), {});
    const data = analysisRes(res);
    return data;
  },

  // 标记消息为已读
  async markAsRead(messageId) {
    const res = await request.post(message.markAsRead.replace('{messageId}', messageId), {});
    const data = analysisRes(res);
    return data;
  },

  // 标记所有消息为已读
  async markAllAsRead(userId) {
    const res = await request.post(message.markAllAsRead.replace('{userId}', userId), {});
    const data = analysisRes(res);
    return data;
  },

  // 删除消息
  async deleteMessage(messageId) {
    const res = await request.delete(message.delete.replace('{messageId}', messageId));
    const data = analysisRes(res);
    return data;
  },

  // 获取未读消息数量
  async getUnreadCount(userId) {
    const res = await request.get(message.unreadCount.replace('{userId}', userId), {});
    const data = analysisRes(res);
    return data;
  },
};
