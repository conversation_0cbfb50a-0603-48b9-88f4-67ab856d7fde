import request, { analysisRes } from '../request';
import config from '../config';

const { employee } = config.apiUrls;

export default {
  /**
   * 获取可选择的员工列表
   * @param {number} customerId 客户ID
   * @param {object} params 查询参数
   * @param {number} [params.serviceTypeId] 服务类型ID，用于筛选特定服务类型的员工
   * @param {number} [params.current] 当前页码，默认为1
   * @param {number} [params.pageSize] 每页大小，默认为10
   * @returns {Promise<any>} 返回员工列表
   */
  async getList(customerId, params = {}) {
    const res = await request.get(
      employee.list.replace('{customerId}', customerId),
      params
    );
    return analysisRes(res);
  }
};
