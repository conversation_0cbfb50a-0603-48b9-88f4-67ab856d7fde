<view class="container containerpet">
  <!-- 查看全部服务选项 -->
  <view wx:if="{{isService}}" class="flex flex-wrap items-stretch diygw-col-24 flex-pet all-services-option {{checkedId === null ? 'choosen':''}}" bind:tap="selectAllServices">
    <view class="picker-item" wx:if="{{checkedId === null}}" class="choose-icon">
      <image src="//xian7.zos.ctyun.cn/pet/static/checked.png" class="diygw-image diygw-col-0" mode="widthFix"></image>
    </view>
    <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
      <image src="//xian7.zos.ctyun.cn/pet/static/nogoods.png" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
    </view>
    <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex48-clz">
      <text class="diygw-col-0 text23-clz">查看全部服务</text>
      <view class="flex flex-wrap diygw-col-24 flex49-clz petInfo">
        <text class="petInfoAge">不限宠物类型</text>
      </view>
      <view class="flex flex-wrap diygw-col-24 lastServiceInfo">
        <text class="lastServiceTime">显示所有可用服务</text>
      </view>
    </view>
    <view wx:if="{{checkedId !== null}}" class="selectPet" bind:tap="selectAllServices">查看全部服务</view>
    <view wx:else></view>
  </view>

  <view wx:if="{{list.rows && list.rows.length>0}}" class="flex flex-wrap diygw-col-24 flex-direction-column">
    <view wx:for="{{list.rows}}" wx:for-item="item" wx:for-index="index" wx:key="index" class="flex flex-wrap items-stretch diygw-col-24 flex-pet {{item.id === checkedId ? 'choosen':'rgba(255, 192, 218, 0.3)'}}" style="background-color: {{item.gender=== 1 ? 'rgba(122, 221, 252, 0.3)':item.gender===2?'rgba(255, 192, 218, 0.3)':'rgba(255, 235, 59, 0.3)'}};" data-item="{{item}}" bind:tap="handlePetTap">
      <view class="picker-item" wx:if="{{item.id === checkedId}}" class="choose-icon">
        <image src="//xian7.zos.ctyun.cn/pet/static/checked.png" class="diygw-image diygw-col-0" mode="widthFix"></image>
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
        <image src="{{item.avatar || siteinfo.constant.pet[item.type]}}" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex48-clz">
        <text class="diygw-col-0 text23-clz"> {{item.name}} </text>
        <view class="flex flex-wrap diygw-col-24 flex49-clz petInfo">
          <text class="petInfoAge">{{item.formattedBri}}</text><text>{{item.gender === 1 ? '弟弟' : item.gender === 2 ? '妹妹' : '未知'}}
          </text>
        </view>
        <view class="flex flex-wrap diygw-col-24 lastServiceInfo">
          <text class="lastServiceTime">{{item.formattedLastServiceTime}}</text>
        </view>
      </view>
      <view wx:if="{{!isService}}">
        <view class="editIcon" bind:tap="editRedirect" data-item="{{item}}">
          <text class="icon6-clz diy-icon-writefill"></text>
          <text class="edit-text">修改</text>
        </view>
        <view class="deleteIcon" bind:tap="deletePetModal" data-item="{{item}}">
          <text class="icon6-clz diy-icon-delete"></text>
          <text class="delete-text">删除</text>
        </view>
      </view>
      <view wx:else>
        <view wx:if="{{item.id !== checkedId}}" class="selectPet" data-item="{{item}}" bind:tap="selectPet">选择该宠物</view>
        <view wx:else class="cancelSelectPet" data-item="{{item}}" bind:tap="selectAllServices">取消选择</view>
      </view>
    </view>
  </view>
  <view wx:else class="flex flex-wrap diygw-col-24 items-center justify-center direction-column">
    <empty icon="//xian7.zos.ctyun.cn/pet/static/nopet.png" text="暂无宠物信息" class="empty" />
  </view>
  <view class="clearfix"></view>
  <view class="flex flex-wrap diygw-col-24 items-end diygw-bottom flex1-clz">
    <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex12-clz">
      <button class="diygw-col-24 bg-none btn-clz diygw-btn-default" bind:tap="redirect">添加宠物</button>
    </view>
  </view>
  <view class="clearfix"></view>
</view>